# [ scenes/UI/credits/credits.gd ]

extends Control

signal back_pressed

@export var scroll_speed: float = 80.0

@onready var credits_text = %CreditsText

var start_y_position: float
var is_scrolling: bool = false


func _ready():
	start_y_position = get_viewport_rect().size.y
	credits_text.position.y = start_y_position


# 크레딧 패널이 보일 때 main_menu.gd에서 직접 호출할 함수입니다.
func start_credits():
	# 위치를 초기화하고 스크롤을 시작합니다.
	credits_text.position.y = start_y_position
	is_scrolling = true


func _process(delta):
	if not is_scrolling or not visible:
		return

	credits_text.position.y -= scroll_speed * delta

	if credits_text.get_rect().end.y < 0:
		is_scrolling = false
		back_pressed.emit()


func _unhandled_input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		if is_scrolling: # 스크롤 중에만 중단하도록
			is_scrolling = false
			get_viewport().set_input_as_handled()
			back_pressed.emit()
