[gd_scene load_steps=4 format=3 uid="uid://yc071iw5658w"]

[ext_resource type="Script" uid="uid://cpa60po0m6gpr" path="res://scenes/UI/in-game_UI/HUD/map_view.gd" id="1_0likk"]
[ext_resource type="Resource" uid="uid://ck0tqwb38jwob" path="res://data/map_color_data.tres" id="2_qvjsw"]
[ext_resource type="Texture2D" uid="uid://sdk18uih4nap" path="res://assets/graphic/world/icon/player_minimap_icon.png" id="3_wgf7o"]

[node name="MapView" type="Control" groups=["map_view"]]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0likk")
map_colors = ExtResource("2_qvjsw")

[node name="MapBackground" type="TextureRect" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="FogOverlay" type="TextureRect" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="PlayerIcon" type="TextureRect" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -7.5
offset_top = -7.5
offset_right = 7.5
offset_bottom = 7.5
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_wgf7o")
