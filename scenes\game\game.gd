# scenes/game/game.gd
extends Node

@onready var player = $World/Player
@onready var player_hud = $PlayerHUD

func _ready():
	if not is_instance_valid(player) or not is_instance_valid(player_hud):
		print("에러: Player 또는 PlayerHUD 노드를 찾을 수 없습니다.")
		return

	# --- 1. 체력 시스템 연결 ---
	var health_system = player.get_node("health_system")
	var health_bar = player_hud.get_node("HealthBar") 

	if is_instance_valid(health_system) and is_instance_valid(health_bar):
		health_system.health_changed.connect(health_bar.update_bar)
		health_system.max_health_changed.connect(health_bar.update_max_value)
		health_bar.initialize(health_system.current_health, health_system.max_health)
	else:
		print("경고: health_system 또는 HealthBar를 찾을 수 없습니다.")

	# --- 2. 마나 시스템 연결 ---
	var mana_system = player.get_node("mana_system")
	var mana_bar = player_hud.get_node("ManaBar") 

	if is_instance_valid(mana_system) and is_instance_valid(mana_bar):
		mana_system.mana_changed.connect(mana_bar.update_bar)
		# [수정 완료] 이제 ManaSystem의 'max_mana_changed' 신호에 정상적으로 연결됩니다.
		mana_system.max_mana_changed.connect(mana_bar.update_max_value)
		mana_bar.initialize(mana_system.current_mana, mana_system.max_mana)
	else:
		print("경고: mana_system 또는 ManaBar를 찾을 수 없습니다.")
