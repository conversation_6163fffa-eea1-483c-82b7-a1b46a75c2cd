# scenes/game/player/player.gd
extends CharacterBody2D

# --- 노드 참조 ---
@export var visuals: Skeleton2D
@onready var animation_player: AnimationPlayer = %AnimationPlayer
@onready var vision_timer: Timer = $VisionTimer

# --- 하위 시스템 참조 ---
@onready var stat_system: StatSystem = %stat_system
@onready var health_system = %health_system
@onready var mana_system = %mana_system
@onready var character_appearance = %character_appearance
@onready var character_equipment = %character_equipment

# --- 애니메이션 리소스 ---
@export var male_anim_library: AnimationLibrary
@export var female_anim_library: AnimationLibrary

# --- 내부 변수 ---
var last_direction = Vector2(0, 1)
var anim_prefix: String

func _ready():
	# [수정] GameManager의 캐릭터 이름을 덮어쓰던 문제의 코드를 삭제했습니다.
	# GameManager.current_character_name = "Player" <- 이 줄을 삭제!

	var gender = GameManager.selected_character_gender
	if gender == "female" and female_anim_library:
		anim_prefix = "female_animations"
		if not animation_player.has_animation_library(anim_prefix):
			animation_player.add_animation_library(anim_prefix, female_anim_library)
	else:
		anim_prefix = "male_animations"
		if not animation_player.has_animation_library(anim_prefix):
			animation_player.add_animation_library(anim_prefix, male_anim_library)
	
	update_animation(Vector2.ZERO)
	
	vision_timer.timeout.connect(_on_vision_timer_timeout)

func _physics_process(_delta):
	var direction = Input.get_vector("move_left", "move_right", "move_up", "move_down")
	velocity = direction * stat_system.current_move_speed
	move_and_slide()
	update_animation(direction)

func update_animation(direction: Vector2):
	if direction.length() > 0:
		last_direction = direction
	var anim_name = "idle_down"
	if direction.length() > 0:
		if abs(last_direction.x) >= abs(last_direction.y):
			anim_name = "walk_side"
		else:
			if last_direction.y > 0:
				anim_name = "walk_down"
			else:
				anim_name = "walk_up"
	else:
		if abs(last_direction.x) >= abs(last_direction.y):
			anim_name = "idle_side"
		else:
			if last_direction.y > 0:
				anim_name = "idle_down"
			else:
				anim_name = "idle_up"

	if visuals.scale.x * last_direction.x < 0:
		visuals.scale.x *= -1

	animation_player.play(anim_prefix + "/" + anim_name)

func _on_vision_timer_timeout():
	var tile_size = MapDataManager.tile_size # <- 올바르게 동적 타일 크기 사용 중
	if tile_size == 0: return

	var camera = get_viewport().get_camera_2d()
	if not camera: return

	var view_rect = camera.get_viewport_rect()
	var view_top_left_world = camera.get_screen_center_position() - view_rect.size / 2
	var view_bottom_right_world = view_top_left_world + view_rect.size

	var top_left_tile = Vector2i(view_top_left_world / tile_size)
	var bottom_right_tile = Vector2i(view_bottom_right_world / tile_size)

	var revealed_tiles_in_sight = []
	
	for y in range(top_left_tile.y, bottom_right_tile.y + 1):
		for x in range(top_left_tile.x, bottom_right_tile.x + 1):
			revealed_tiles_in_sight.append(Vector2i(x, y))

	ExplorationManager.reveal_tiles(revealed_tiles_in_sight)
