# scenes/UI/in-game_UI/worldmap.gd
extends Control

signal map_closed

@onready var position_label: Label = %PositionLabel
@onready var biome_label: Label = %BiomeLabel

var player: CharacterBody2D
var biome_manager: Node

func _ready():
	process_mode = Node.PROCESS_MODE_ALWAYS
	
	# [수정] GameManager의 player_node 변수를 사용하고, BiomeManager는 씬 트리에서 직접 찾습니다.
	player = get_tree().get_first_node_in_group("player") as CharacterBody2D
	biome_manager = get_tree().get_root().find_child("BiomeManager", true, false)

	if not is_instance_valid(player) or not is_instance_valid(biome_manager):
		printerr("Worldmap: Player or BiomeManager node not found in the scene tree!")
		if not is_instance_valid(player):
			position_label.text = "Player not found"
		if not is_instance_valid(biome_manager):
			biome_label.text = "BiomeManager not found"
		set_process(false)

func _process(_delta):
	if is_instance_valid(player) and is_instance_valid(biome_manager):
		var player_pos = player.global_position
		if MapDataManager.tile_size == 0: return

		var tile_pos = Vector2i(player_pos / MapDataManager.tile_size)
		var current_biome = biome_manager.get_biome(tile_pos)
		
		position_label.text = "Position: (%d, %d)" % [tile_pos.x, tile_pos.y]
		if current_biome:
			biome_label.text = "Biome: %s" % current_biome.name
		else:
			biome_label.text = "Biome: Unknown"

func _unhandled_input(event: InputEvent):
	if event.is_action_pressed("toggle_worldmap") or event.is_action_pressed("ui_cancel"):
		get_viewport().set_input_as_handled()
		close_map()

func close_map():
	emit_signal("map_closed")
	queue_free()
