extends Node

signal map_data_updated
signal map_cache_generated
signal map_fog_updated

# [삭제] const MAP_COLORS = preload(...) 라인을 삭제합니다.

var tile_size: int = 32
var current_map_info: Dictionary = {}
var current_map_colors: Resource = null # [추가] 현재 맵의 색상 리소스를 저장할 변수

var cached_map_texture: ImageTexture = null
var cached_fog_texture: ImageTexture = null
var cached_fog_image: Image = null

var map_generation_thread: Thread

func _process(_delta):
	if map_generation_thread and not map_generation_thread.is_alive():
		_on_thread_completed()

# [수정] 맵 데이터를 설정할 때, map_colors_res 파라미터를 추가로 받습니다.
func set_current_map_data(data: Dictionary, origin: Vector2i, width: int, height: int, map_colors_res: Resource):
	clear_map_cache()
	
	current_map_info = {
		"data": data,
		"origin": origin,
		"width": width,
		"height": height
	}
	current_map_colors = map_colors_res # [추가] 전달받은 리소스를 변수에 저장
	
	map_data_updated.emit()
	generate_map_cache_async()

func update_fog_of_war(newly_revealed_tiles: Array):
	if not cached_fog_image: return
	var map_info = get_current_map_info()
	if not map_info: return
	var origin = map_info.get("origin", Vector2i.ZERO)
	var changed = false
	for world_tile_pos in newly_revealed_tiles:
		var image_pos = world_tile_pos - origin
		if image_pos.x >= 0 and image_pos.x < cached_fog_image.get_width() and \
		   image_pos.y >= 0 and image_pos.y < cached_fog_image.get_height():
			if cached_fog_image.get_pixelv(image_pos).a == 1.0:
				cached_fog_image.set_pixelv(image_pos, Color(0, 0, 0, 0))
				changed = true
	if changed:
		cached_fog_texture = ImageTexture.create_from_image(cached_fog_image)
		map_fog_updated.emit()

func get_current_map_info() -> Dictionary:
	return current_map_info

func clear_map_cache():
	current_map_info = {}
	current_map_colors = null # [추가] 캐시를 비울 때 색상 정보도 비웁니다.
	cached_map_texture = null
	cached_fog_texture = null
	cached_fog_image = null

func generate_map_cache_async():
	if cached_map_texture != null: return
	if map_generation_thread and map_generation_thread.is_alive(): return
	map_generation_thread = Thread.new()
	map_generation_thread.start(_thread_generate_textures.bind())

func _thread_generate_textures() -> Dictionary:
	var map_info = get_current_map_info()
	if not map_info: return {}
	
	if not is_instance_valid(current_map_colors):
		printerr("Map Colors resource is not set in MapDataManager!")
		return {}

	var width = map_info.get("width", 0)
	var height = map_info.get("height", 0)
	if width == 0 or height == 0: return {}
	
	var map_image = Image.create(width, height, false, Image.FORMAT_RGBA8)
	var world_data = map_info.get("data", {})
	var origin = map_info.get("origin", Vector2i.ZERO)
	for y in range(height):
		for x in range(width):
			var world_coord = Vector2i(x, y) + origin
			
			# [핵심 수정] str(world_coord)가 아닌, Vector2i 타입인 world_coord로 직접 키를 찾습니다.
			if world_data.has(world_coord):
				var tile_info = world_data[world_coord]
				var tile_type = tile_info.get("type")
				
				var color = current_map_colors.color_map.get(tile_type, Color.BLACK)
				map_image.set_pixel(x, y, color)

	var fog_img = Image.create(width, height, false, Image.FORMAT_RGBA8)
	fog_img.fill(Color.BLACK)
	var initial_tiles = ExplorationManager.get_initial_revealed_tiles()
	for world_tile_pos in initial_tiles:
		var image_pos = world_tile_pos - origin
		if image_pos.x >= 0 and image_pos.x < width and \
		   image_pos.y >= 0 and image_pos.y < height:
			fog_img.set_pixelv(image_pos, Color(0, 0, 0, 0))

	return {"map_image": map_image, "fog_image": fog_img}

func _on_thread_completed():
	var image_data: Dictionary = map_generation_thread.wait_to_finish()
	map_generation_thread = null
	if not image_data or not image_data.has("map_image"):
		printerr("Map cache generation failed.")
		return
	var map_image = image_data["map_image"]
	cached_map_texture = ImageTexture.create_from_image(map_image)
	cached_fog_image = image_data["fog_image"]
	cached_fog_texture = ImageTexture.create_from_image(cached_fog_image)
	map_cache_generated.emit()
