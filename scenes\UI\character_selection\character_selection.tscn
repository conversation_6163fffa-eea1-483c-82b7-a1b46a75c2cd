[gd_scene load_steps=3 format=3 uid="uid://dbfvim6clm6ke"]

[ext_resource type="Script" uid="uid://bg3y03xubylqx" path="res://scenes/UI/character_selection/character_selection.gd" id="1_hxfdk"]
[ext_resource type="PackedScene" uid="uid://dlamnm50x08uc" path="res://scenes/UI/character_selection/character_slot.tscn" id="2_ew75n"]

[node name="character_selection" type="Control"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_hxfdk")
character_slot_scene = ExtResource("2_ew75n")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -256.0
offset_right = 150.0
offset_bottom = 256.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "CHARACTER_SELECT_TITLE"
horizontal_alignment = 1

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 450)
layout_mode = 2
size_flags_vertical = 3

[node name="CharacterList" type="VBoxContainer" parent="VBoxContainer/ScrollContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="BackButton" type="Button" parent="VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "UI_BACK"

[node name="CreateButton" type="Button" parent="VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "UI_CREATE"

[node name="SelectButton" type="Button" parent="VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
disabled = true
text = "UI_SELECT"

[node name="DeleteConfirmationDialog" type="ConfirmationDialog" parent="."]
unique_name_in_owner = true
title = "UI_CONFIRM_TITLE"
initial_position = 1
size = Vector2i(300, 150)
ok_button_text = "UI_OK"
cancel_button_text = "UI_CANCEL"
