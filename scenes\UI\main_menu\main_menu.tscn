[gd_scene load_steps=10 format=3 uid="uid://ca4yjkha0d5v0"]

[ext_resource type="Script" uid="uid://xx7ooifpiqmk" path="res://scenes/UI/main_menu/main_menu.gd" id="1_qwnig"]
[ext_resource type="Texture2D" uid="uid://cw2t3a83ao2la" path="res://assets/graphic/UI/background/main_background.png" id="2_tfcbk"]
[ext_resource type="PackedScene" uid="uid://dbfvim6clm6ke" path="res://scenes/UI/character_selection/character_selection.tscn" id="3_e2p27"]
[ext_resource type="PackedScene" uid="uid://dkn8x6ivnbhly" path="res://scenes/UI/world_selection/world_selection.tscn" id="4_ngj32"]
[ext_resource type="PackedScene" uid="uid://wr2xhojvwldv" path="res://scenes/UI/character_selection/character_creation.tscn" id="5_e2p27"]
[ext_resource type="PackedScene" uid="uid://6dtfvs1lsvr2" path="res://scenes/UI/world_selection/world_creation.tscn" id="6_ngj32"]
[ext_resource type="PackedScene" uid="uid://dc8ax8mbqphwb" path="res://scenes/UI/settings_menu/settings_menu.tscn" id="7_bm8t2"]
[ext_resource type="PackedScene" uid="uid://c1pdjg7twg0hm" path="res://scenes/UI/credits/credits.tscn" id="8_srge5"]
[ext_resource type="PackedScene" uid="uid://bxm8y4n5qr7vw" path="res://scenes/UI/world_generation/world_generation_screen.tscn" id="9_world_gen"]

[node name="main_menu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_qwnig")

[node name="background" type="TextureRect" parent="."]
z_index = -1
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -960.0
offset_top = -540.0
offset_right = 960.0
offset_bottom = 540.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("2_tfcbk")

[node name="main_menu" type="VBoxContainer" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -45.0
offset_top = -50.5
offset_right = 45.0
offset_bottom = 50.5
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="StartButton" type="Button" parent="main_menu"]
unique_name_in_owner = true
layout_mode = 2
text = "MAIN_MENU_START"

[node name="SettingsButton" type="Button" parent="main_menu"]
unique_name_in_owner = true
layout_mode = 2
text = "UI_SETTINGS"

[node name="CreditsButton" type="Button" parent="main_menu"]
unique_name_in_owner = true
layout_mode = 2
text = "MAIN_MENU_CREDITS"

[node name="Test3DButton" type="Button" parent="main_menu"]
unique_name_in_owner = true
layout_mode = 2
text = "3D Test"

[node name="ExitButton" type="Button" parent="main_menu"]
unique_name_in_owner = true
layout_mode = 2
text = "UI_EXIT"

[node name="character_selection" parent="." instance=ExtResource("3_e2p27")]
unique_name_in_owner = true
visible = false
layout_mode = 1

[node name="character_creation" parent="." instance=ExtResource("5_e2p27")]
unique_name_in_owner = true
visible = false
layout_mode = 1

[node name="world_selection" parent="." instance=ExtResource("4_ngj32")]
unique_name_in_owner = true
visible = false
layout_mode = 1

[node name="world_creation" parent="." instance=ExtResource("6_ngj32")]
unique_name_in_owner = true
visible = false
layout_mode = 1

[node name="settings_menu" parent="." instance=ExtResource("7_bm8t2")]
unique_name_in_owner = true
visible = false
layout_mode = 1

[node name="credits" parent="." instance=ExtResource("8_srge5")]
unique_name_in_owner = true
visible = false
layout_mode = 1

[node name="world_generation_screen" parent="." instance=ExtResource("9_world_gen")]
unique_name_in_owner = true
visible = false
layout_mode = 1
