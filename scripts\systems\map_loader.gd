# scripts/systems/map_loader.gd
extends Node
class_name MapLoader

# 저장 파일로부터 데이터를 불러와 게임에서 사용할 수 있는 형식으로 변환합니다.
static func get_data_from_save(tile_data_from_file: Dictionary) -> Dictionary:
	var generated_tile_types = {}
	var TileType = preload("res://scripts/data_definitions/tile_data.gd")

	for cell_pos_str in tile_data_from_file:
		var parts = cell_pos_str.trim_prefix("(").trim_suffix(")").split(",")
		if parts.size() != 2:
			continue
		
		var x_str = parts[0].strip_edges()
		var y_str = parts[1].strip_edges()

		var cell_pos = Vector2i(int(x_str), int(y_str))
		var data = tile_data_from_file[cell_pos_str]
		if not data.has("type"):
			continue

		var tile_type_enum = int(data["type"])
		var tile_name = ""
		
		match tile_type_enum:
			TileType.Tile.GRASS: tile_name = "grass"
			TileType.Tile.DIRT: tile_name = "dirt"
			TileType.Tile.WATER: tile_name = "water"
			TileType.Tile.ICE: tile_name = "ice"
			TileType.Tile.SNOW: tile_name = "snow"
			_:
				continue

		if not tile_name.is_empty():
			generated_tile_types[cell_pos] = tile_name

	return generated_tile_types

# 게임 내 타일 데이터를 미니맵 등에서 사용할 형식으로 변환합니다.
static func get_map_data_for_tilemap(generated_tiles: Dictionary) -> Dictionary:
	var map_data = {}
	var TileType = preload("res://scripts/data_definitions/tile_data.gd")
	for cell_pos in generated_tiles:
		var tile_name = generated_tiles[cell_pos]
		var tile_type_enum
		match tile_name:
			"grass": tile_type_enum = TileType.Tile.GRASS
			"dirt": tile_type_enum = TileType.Tile.DIRT
			"water": tile_type_enum = TileType.Tile.WATER
			"ice": tile_type_enum = TileType.Tile.ICE
			"snow": tile_type_enum = TileType.Tile.SNOW
			_:
				continue
		# [핵심 수정] Vector2i 키를 문자열로 변환하지 않고, 그대로 사용합니다.
		# 이 데이터는 MapDataManager에서 미니맵을 그릴 때 사용됩니다.
		map_data[cell_pos] = {"type": tile_type_enum}
	return map_data

# 월드 이름과 생성된 타일 데이터를 받아 파일로 저장합니다.
static func save_map_data(world_name: String, generated_tiles: Dictionary):
	if generated_tiles.is_empty():
		printerr("Loader: Generated tiles data is empty. Aborting save.")
		return

	# 1. 먼저 타일 이름을 Enum 숫자로 변환합니다. (이때 키는 아직 Vector2i 입니다)
	var map_data_with_enums = get_map_data_for_tilemap(generated_tiles)
	
	# 2. [핵심 수정] 파일 저장을 위해 새로운 Dictionary를 만들고, Vector2i 키를 String으로 변환합니다.
	var map_data_to_save = {}
	for cell_pos in map_data_with_enums:
		map_data_to_save[str(cell_pos)] = map_data_with_enums[cell_pos]

	if map_data_to_save.is_empty():
		printerr("Loader: Map data is empty after conversion. Aborting save.")
		return

	var world_save_data = {
		"tiles": map_data_to_save
	}
	# 참고: SaveManager.save_world_data는 두 개의 인자를 받도록 수정되어야 할 수 있습니다.
	# (world_save_data, world_name)
	SaveManager.save_world_data(world_save_data, world_name)
	print("Map data saved for world: '", world_name, "'")
