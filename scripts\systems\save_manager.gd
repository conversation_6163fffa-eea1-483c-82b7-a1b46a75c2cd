# scripts/systems/save_manager.gd
extends Node

const WORLDS_DIR = "user://worlds/"
const PLAYERS_DIR = "user://players/"
const WORLD_EXTENSION = ".map"
const PLAYER_EXTENSION = ".save"

func ensure_dir_exists(path: String):
	DirAccess.make_dir_absolute(path)

# ==============================================================================
# 월드 데이터 관리
# ==============================================================================
func save_world_data(world_data: Dictionary, world_name: String):
	ensure_dir_exists(WORLDS_DIR)
	var file_path = WORLDS_DIR + world_name + WORLD_EXTENSION
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if not file:
		printerr("ERROR: Cannot open world file for writing: ", file_path)
		return
	# [수정] JSON을 저장할 때 "\t"를 추가하여 사람이 읽기 좋은 형태로 저장합니다 (pretty print).
	var json_string = JSON.stringify(world_data, "\t")
	file.store_string(json_string)
	print("World data saved: ", file_path)

func load_world_data(world_name: String) -> Dictionary:
	var file_path = WORLDS_DIR + world_name + WORLD_EXTENSION
	if not FileAccess.file_exists(file_path):
		return {}
	var file = FileAccess.open(file_path, FileAccess.READ)
	if not file:
		printerr("ERROR: Cannot open world file for reading: ", file_path)
		return {}
	var json_string = file.get_as_text()
	var data = JSON.parse_string(json_string)
	return data if data else {}

# ... (이하 플레이어 데이터 관리 코드는 그대로 유지) ...
func get_saved_world_list() -> Array[String]:
	ensure_dir_exists(WORLDS_DIR)
	var saved_worlds: Array[String] = []
	var dir = DirAccess.open(WORLDS_DIR)
	if dir:
		for file_name in dir.get_files():
			if file_name.ends_with(WORLD_EXTENSION):
				saved_worlds.append(file_name.trim_suffix(WORLD_EXTENSION))
	return saved_worlds

func delete_world(world_name: String):
	var file_path = WORLDS_DIR + world_name + WORLD_EXTENSION
	if FileAccess.file_exists(file_path):
		DirAccess.remove_absolute(file_path)

# ==============================================================================
# 플레이어 데이터 관리
# ==============================================================================
func save_character_data(character_data: Dictionary, character_name: String):
	if character_name.is_empty():
		printerr("ERROR: Character name is empty, cannot save.")
		return
	ensure_dir_exists(PLAYERS_DIR)
	var file_path = PLAYERS_DIR + character_name + PLAYER_EXTENSION
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if not file:
		printerr("ERROR: Cannot open character file for writing: ", file_path)
		return
	var json_string = JSON.stringify(character_data)
	file.store_string(json_string)
	print("Character saved: ", file_path)

func load_character_data(character_name: String) -> Dictionary:
	var file_path = PLAYERS_DIR + character_name + PLAYER_EXTENSION
	if not FileAccess.file_exists(file_path):
		return {}
	var file = FileAccess.open(file_path, FileAccess.READ)
	if not file:
		printerr("ERROR: Cannot open character file for reading: ", file_path)
		return {}
	var json_string = file.get_as_text()
	var data = JSON.parse_string(json_string)
	return data if data else {}

func get_saved_character_list() -> Array[String]:
	ensure_dir_exists(PLAYERS_DIR)
	var characters: Array[String] = []
	var dir = DirAccess.open(PLAYERS_DIR)
	if dir:
		for file_name in dir.get_files():
			if file_name.ends_with(PLAYER_EXTENSION):
				characters.append(file_name.trim_suffix(PLAYER_EXTENSION))
	return characters

func delete_character(character_name: String):
	var file_path = PLAYERS_DIR + character_name + PLAYER_EXTENSION
	if FileAccess.file_exists(file_path):
		DirAccess.remove_absolute(file_path)
