# scripts/systems/health_system.gd
extends Node
class_name HealthSystem

# --- 신호 정의 ---
signal health_changed(current_health)
signal max_health_changed(new_max_health) # [추가] 최대 체력이 바뀔 때를 위한 신호
signal died

# --- 변수 ---
var current_health: float
var max_health: float:
	# max_health 변수의 값이 변경될 때마다 자동으로 이 코드를 실행합니다. (Setter)
	set(value):
		if max_health != value:
			max_health = value
			max_health_changed.emit(max_health) # 값이 바뀌면 신호를 보냅니다.

# --- 노드 참조 ---
@onready var stat_system: StatSystem = get_parent().get_node("stat_system")


func _ready():
	if stat_system:
		# [중요] StatSystem이 보내는 신호에 연결하여 max_health를 동적으로 업데이트합니다.
		# stat_system 스크립트의 실제 신호 이름에 따라 아래 이름을 수정해야 할 수 있습니다.
		stat_system.current_max_health_changed.connect(_on_max_health_changed)
		
		# 초기값 설정 (Setter를 통해 신호가 발생하도록 self를 사용)
		self.max_health = stat_system.current_max_health
		current_health = max_health


# StatSystem으로부터 최대 체력이 변경되었다는 신호를 받으면 이 함수가 실행됩니다.
func _on_max_health_changed(new_max_health: float):
	# Setter를 호출하여 max_health 값을 업데이트하고 신호를 발생시킵니다.
	self.max_health = new_max_health
	
	# 현재 체력이 새 최대 체력보다 높으면 조정해줍니다.
	if current_health > max_health:
		current_health = max_health
		health_changed.emit(current_health)


func take_damage(amount):
	current_health -= amount
	if current_health <= 0:
		current_health = 0
		died.emit()
	health_changed.emit(current_health)
