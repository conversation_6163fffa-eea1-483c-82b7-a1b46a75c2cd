# scenes/UI/world_generation/world_generation_screen.gd
extends Control

signal generation_complete

# UI 노드들
@onready var title_label = %TitleLabel
@onready var progress_bar = %ProgressBar
@onready var status_label = %StatusLabel
@onready var info_label = %InfoLabel

# 생성 관련 변수들
var world_name: String = ""
var generation_steps: Array[Dictionary] = []
var current_step: int = 0
var is_generating: bool = false

func _ready():
	# 번역된 텍스트로 초기화
	title_label.text = tr("WORLD_GENERATION_TITLE")
	status_label.text = tr("WORLD_GENERATION_INITIALIZING")
	info_label.text = tr("WORLD_GENERATION_INFO")
	progress_bar.value = 0

	# 생성 단계들 정의
	generation_steps = [
		{"key": "WORLD_GENERATION_INITIALIZING", "duration": 0.5},
		{"key": "WORLD_GENERATION_BIOMES", "duration": 1.5},
		{"key": "WORLD_GENERATION_TERRAIN", "duration": 2.0},
		{"key": "WORLD_GENERATION_RIVERS", "duration": 1.0},
		{"key": "WORLD_GENERATION_LAKES", "duration": 0.8},
		{"key": "WORLD_GENERATION_FINALIZING", "duration": 1.2}
	]

	# GameManager에서 설정된 월드 이름으로 자동 시작
	if not GameManager.current_world_name.is_empty():
		start_generation(GameManager.current_world_name)

func start_generation(p_world_name: String):
	world_name = p_world_name
	is_generating = true
	current_step = 0
	progress_bar.value = 0
	
	# 첫 번째 단계 시작
	_start_next_step()

func _start_next_step():
	if current_step >= generation_steps.size():
		_complete_generation()
		return

	var step = generation_steps[current_step]
	status_label.text = tr(step.key)

	# 진행률 업데이트 (부드러운 애니메이션)
	var target_progress = float(current_step) / float(generation_steps.size()) * 100.0
	var tween = create_tween()
	tween.tween_property(progress_bar, "value", target_progress, 0.3)

	# 단계 지속 시간만큼 대기 후 다음 단계로
	await get_tree().create_timer(step.duration).timeout

	current_step += 1
	_start_next_step()

func _complete_generation():
	# 실제 세계 생성 로직 호출
	_generate_world_data()

	# 완료 상태 표시
	status_label.text = tr("WORLD_GENERATION_COMPLETE")

	# 진행률을 100%로 부드럽게 애니메이션
	var tween = create_tween()
	tween.tween_property(progress_bar, "value", 100.0, 0.5)
	await tween.finished

	# 잠시 대기 후 완료 신호 발송
	await get_tree().create_timer(1.0).timeout
	is_generating = false
	emit_signal("generation_complete")

func _generate_world_data():
	# 실제 세계 데이터 생성
	# 기존 GameManager.new_game() 로직을 여기서 호출
	if GameManager.current_character_name.is_empty():
		printerr("세계 생성 오류: 캐릭터가 선택되지 않았습니다!")
		GameManager.current_character_name = "Player"
	
	GameManager.current_world_name = world_name
	
	var world_data = SaveManager.load_world_data(world_name)
	if world_data.is_empty():
		world_data = {"world_name": world_name}
		SaveManager.save_world_data(world_data, world_name)
	
	var char_data = SaveManager.load_character_data(GameManager.current_character_name)
	if not char_data.has("name"):
		char_data["name"] = GameManager.current_character_name
	if not char_data.has("exploration_data"):
		char_data["exploration_data"] = {}
	char_data["exploration_data"][GameManager.current_world_name] = []
	SaveManager.save_character_data(char_data, GameManager.current_character_name)
	
	ExplorationManager.clear_data()

func _input(event: InputEvent):
	# ESC 키로 취소 방지 (생성 중에는 취소할 수 없음)
	if event.is_action_pressed("ui_cancel") and is_generating:
		get_viewport().set_input_as_handled()
