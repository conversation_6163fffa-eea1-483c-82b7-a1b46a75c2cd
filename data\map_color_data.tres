[gd_resource type="Resource" script_class="MapColorData" load_steps=2 format=3 uid="uid://ck0tqwb38jwob"]

[ext_resource type="Script" uid="uid://deagr6wvwth6a" path="res://scripts/data_definitions/map_color_data.gd" id="1_q6be1"]

[resource]
script = ExtResource("1_q6be1")
color_map = {
0: Color(0.470588, 0.262745, 0.0823529, 1),
1: Color(0.458824, 0.976471, 0.301961, 1),
2: Color(0, 0.137255, 0.960784, 1),
3: Color(0, 0.635294, 0.909804, 1),
4: Color(1, 1, 1, 1)
}
