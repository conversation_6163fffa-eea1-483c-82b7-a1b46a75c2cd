extends Control

signal creation_requested
signal back_pressed

@export var world_slot_scene: PackedScene

@onready var world_list_container = %WorldList
@onready var new_world_button = %NewWorldButton
@onready var back_button = %BackButton
@onready var select_button = %SelectButton
@onready var delete_confirmation_dialog: ConfirmationDialog = %DeleteConfirmationDialog

var world_to_delete: String
var selected_world_name: String

func _ready():
	select_button.disabled = true
	new_world_button.pressed.connect(_on_new_world_pressed)
	back_button.pressed.connect(_on_back_pressed)
	select_button.pressed.connect(_on_select_button_pressed)
	delete_confirmation_dialog.confirmed.connect(_on_delete_confirmation_dialog_confirmed)
	populate_world_list()

func populate_world_list():
	selected_world_name = ""
	select_button.disabled = true
	
	for child in world_list_container.get_children():
		child.queue_free()
	
	var saved_worlds = SaveManager.get_saved_world_list()
	
	for world_name_str in saved_worlds:
		var slot = world_slot_scene.instantiate()
		slot.world_name = world_name_str
		world_list_container.add_child(slot)
		
		slot.pressed.connect(_on_world_slot_selected.bind(world_name_str))
		slot.double_clicked.connect(_on_world_slot_double_clicked.bind(world_name_str))
		slot.delete_requested.connect(_on_delete_requested.bind(world_name_str))

func _on_world_slot_selected(world_name: String):
	selected_world_name = world_name
	select_button.disabled = false
	
	for slot in world_list_container.get_children():
		if slot.world_name == selected_world_name:
			slot.modulate = Color.WHITE
		else:
			slot.modulate = Color(0.6, 0.6, 0.6)

func _on_world_slot_double_clicked(world_name: String):
	selected_world_name = world_name
	_on_select_button_pressed()

func _on_select_button_pressed():
	if selected_world_name.is_empty():
		return
	GameManager.load_game(selected_world_name)

func _on_delete_requested(world_name: String):
	world_to_delete = world_name
	# [변경] 삭제 확인 메시지를 번역 키와 포매팅을 사용하여 설정합니다.
	delete_confirmation_dialog.dialog_text = tr("WORLD_DELETE_CONFIRM_TEXT") % world_name
	delete_confirmation_dialog.popup_centered()

func _on_new_world_pressed():
	emit_signal("creation_requested")

func _on_back_pressed():
	emit_signal("back_pressed")

func _on_delete_confirmation_dialog_confirmed():
	if not world_to_delete.is_empty():
		SaveManager.delete_world(world_to_delete)
		populate_world_list()
		world_to_delete = ""
