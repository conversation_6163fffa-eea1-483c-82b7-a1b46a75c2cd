[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://bmcde18htidnn"
path="res://.godot/imported/neodgm.ttf-56a2ba7cd44cac636f344001a4de6fa3.fontdata"

[deps]

source_file="res://assets/fonts/neodgm.ttf"
dest_files=["res://.godot/imported/neodgm.ttf-56a2ba7cd44cac636f344001a4de6fa3.fontdata"]

[params]

Rendering=null
antialiasing=0
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=0
subpixel_positioning=0
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[{
"chars": [],
"glyphs": [],
"name": "New Configuration",
"size": Vector2i(16, 0)
}]
language_support={}
script_support={}
opentype_features={}
