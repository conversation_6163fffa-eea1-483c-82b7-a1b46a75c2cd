[gd_scene load_steps=2 format=3 uid="uid://bxm8y4n5qr7vw"]

[ext_resource type="Script" uid="uid://cwyksqydf4y0e" path="res://scenes/UI/world_generation/world_generation_screen.gd" id="1_world_gen"]

[node name="WorldGenerationScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_world_gen")

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer"]
custom_minimum_size = Vector2(400, 200)
layout_mode = 2

[node name="TitleLabel" type="Label" parent="CenterContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
text = "WORLD_GENERATION_TITLE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ProgressContainer" type="VBoxContainer" parent="CenterContainer/VBoxContainer"]
layout_mode = 2

[node name="ProgressBar" type="ProgressBar" parent="CenterContainer/VBoxContainer/ProgressContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(350, 30)
layout_mode = 2
step = 1.0

[node name="StatusLabel" type="Label" parent="CenterContainer/VBoxContainer/ProgressContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 25)
layout_mode = 2
text = "WORLD_GENERATION_INITIALIZING"
horizontal_alignment = 1

[node name="HSeparator2" type="HSeparator" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="InfoLabel" type="Label" parent="CenterContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
text = "WORLD_GENERATION_INFO"
horizontal_alignment = 1
autowrap_mode = 2
