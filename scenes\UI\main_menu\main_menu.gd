extends Control

# --- UI 패널 변수 ---
@onready var main_menu_panel = %main_menu
@onready var character_selection_panel = %character_selection
@onready var world_selection_panel = %world_selection
@onready var character_creation_panel = %character_creation
@onready var world_creation_panel = %world_creation
@onready var settings_panel = %settings_menu
@onready var credits_panel = %credits
@onready var world_generation_screen = %world_generation_screen

# --- 메인 메뉴 버튼 변수 ---
@onready var start_button = %StartButton
@onready var settings_button = %SettingsButton
@onready var credits_button = %CreditsButton
@onready var test_3d_button = %Test3DButton
@onready var exit_button = %ExitButton

# 모든 UI 패널을 관리할 배열
var all_panels: Array[Control]

func _ready():
	# 관리할 모든 패널을 배열에 추가합니다.
	all_panels = [
		main_menu_panel, character_selection_panel, world_selection_panel,
		character_creation_panel, world_creation_panel, settings_panel, credits_panel,
		world_generation_screen
	]

	# --- 패널 신호 연결 ---
	character_selection_panel.connect("character_selected", _on_character_selected)
	character_selection_panel.connect("creation_requested", _on_creation_requested)
	character_selection_panel.connect("back_pressed", _on_character_selection_back)

	character_creation_panel.connect("character_created", _on_character_created)
	character_creation_panel.connect("back_pressed", _on_character_creation_back)

	world_selection_panel.connect("creation_requested", _on_world_creation_requested)
	world_selection_panel.connect("back_pressed", _on_world_selection_back)
	
	world_creation_panel.connect("world_created", _on_world_created)
	world_creation_panel.connect("back_pressed", _on_world_creation_back)

	world_generation_screen.connect("generation_complete", _on_world_generation_complete)

	settings_panel.connect("back_pressed", _on_settings_back_pressed)
	credits_panel.connect("back_pressed", _on_credits_back_pressed)

	UIManager.back_in_menu_requested.connect(_on_back_in_menu_requested)

	# --- 메인 메뉴 버튼 신호 연결 ---
	start_button.pressed.connect(_on_start_button_pressed)
	settings_button.pressed.connect(_on_settings_button_pressed)
	credits_button.pressed.connect(_on_credits_button_pressed)
	test_3d_button.pressed.connect(_on_test_3d_button_pressed)
	exit_button.pressed.connect(_on_exit_button_pressed)
	
	# 시작 시 메인 메뉴만 보여줍니다.
	show_panel(main_menu_panel)

# 특정 패널만 보여주고 나머지는 모두 숨기는 함수
func show_panel(panel_to_show: Control):
	for panel in all_panels:
		if panel == panel_to_show:
			panel.show()
		else:
			panel.hide()

# --- 버튼 및 패널 신호 처리 함수들 ---

func _on_start_button_pressed():
	var char_list = SaveManager.get_saved_character_list()
	if char_list.is_empty():
		show_panel(character_creation_panel)
	else:
		show_panel(character_selection_panel)
		character_selection_panel.populate_character_list()

func _on_settings_button_pressed():
	show_panel(settings_panel)

func _on_credits_button_pressed():
	show_panel(credits_panel)
	# credits_panel에게 스크롤을 시작하라고 명시적으로 알려줍니다.
	credits_panel.start_credits()

func _on_test_3d_button_pressed():
	get_tree().change_scene_to_file("res://scenes/3d_test/test_world_3d.tscn")

func _on_exit_button_pressed():
	get_tree().quit()

func _on_character_selected():
	show_panel(world_selection_panel)
	world_selection_panel.populate_world_list()

func _on_creation_requested():
	show_panel(character_creation_panel)

func _on_character_created():
	show_panel(character_selection_panel)
	character_selection_panel.populate_character_list()

func _on_world_creation_requested():
	show_panel(world_creation_panel)

func _on_world_created():
	show_panel(world_generation_screen)
	world_generation_screen.start_generation(GameManager.current_world_name)

func _on_world_generation_complete():
	show_panel(world_selection_panel)
	world_selection_panel.populate_world_list()

# --- 뒤로 가기 신호 처리 함수들 ---

func _on_back_in_menu_requested():
	# 현재 보이는 패널을 찾아 뒤로가기 동작을 결정합니다.
	for panel in all_panels:
		if panel.visible:
			match panel:
				character_selection_panel: _on_character_selection_back()
				character_creation_panel: _on_character_creation_back()
				world_selection_panel: _on_world_selection_back()
				world_creation_panel: _on_world_creation_back()
				world_generation_screen: _on_world_generation_back()
				settings_panel: _on_settings_back_pressed()
				credits_panel: _on_credits_back_pressed()
			return # 하나의 동작만 수행하고 함수 종료

func _on_character_selection_back():
	show_panel(main_menu_panel)

func _on_character_creation_back():
	var char_list = SaveManager.get_saved_character_list()
	if char_list.is_empty():
		show_panel(main_menu_panel)
	else:
		show_panel(character_selection_panel)

func _on_world_selection_back():
	show_panel(character_selection_panel)

func _on_world_creation_back():
	show_panel(world_selection_panel)

func _on_world_generation_back():
	# 세계 생성 중에는 뒤로가기 불가 (무시)
	pass

func _on_settings_back_pressed():
	show_panel(main_menu_panel)

func _on_credits_back_pressed():
	show_panel(main_menu_panel)
