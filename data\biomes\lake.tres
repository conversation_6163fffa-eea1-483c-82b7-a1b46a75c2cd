[gd_resource type="Resource" script_class="BiomeData" load_steps=4 format=3 uid="uid://dx8gtv0hrre0k"]

[ext_resource type="Script" uid="uid://cv0pplcsu11ts" path="res://scripts/data_definitions/biome_data.gd" id="1_0ilf3"]
[ext_resource type="Script" uid="uid://ytux6w48rnbr" path="res://scripts/data_definitions/tilew_eight_data.gd" id="2_4bddm"]

[sub_resource type="Resource" id="Resource_0ilf3"]
script = ExtResource("2_4bddm")
tile_name = "water"
weight = 1.0
metadata/_custom_type_script = "uid://ytux6w48rnbr"

[resource]
script = ExtResource("1_0ilf3")
biome_name = "Lake"
name = "Lake"
tile_weights = Array[ExtResource("2_4bddm")]([SubResource("Resource_0ilf3")])
optimal_temperature = 0.0
optimal_humidity = 0.0
metadata/_custom_type_script = "uid://cv0pplcsu11ts"
