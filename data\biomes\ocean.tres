[gd_resource type="Resource" script_class="BiomeData" load_steps=4 format=3 uid="uid://b5n3itv58jbl4"]

[ext_resource type="Script" uid="uid://cv0pplcsu11ts" path="res://scripts/data_definitions/biome_data.gd" id="1_rp7t1"]
[ext_resource type="Script" uid="uid://ytux6w48rnbr" path="res://scripts/data_definitions/tilew_eight_data.gd" id="2_1x1lr"]

[sub_resource type="Resource" id="Resource_rp7t1"]
script = ExtResource("2_1x1lr")
tile_name = "water"
weight = 1.0
metadata/_custom_type_script = "uid://ytux6w48rnbr"

[resource]
script = ExtResource("1_rp7t1")
biome_name = "Ocean"
name = "Ocean"
tile_weights = Array[ExtResource("2_1x1lr")]([SubResource("Resource_rp7t1")])
optimal_temperature = 0.0
optimal_humidity = 0.0
metadata/_custom_type_script = "uid://cv0pplcsu11ts"
