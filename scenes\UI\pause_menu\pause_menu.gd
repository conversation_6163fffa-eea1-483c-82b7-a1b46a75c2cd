# scenes/UI/pause_menu/pause_menu.gd
extends Control

signal quit_to_main_menu_pressed
signal menu_closed

@onready var main_menu_container = %MainMenuContainer
@onready var settings_menu = %settings_menu

@onready var resume_button = %ResumeButton
@onready var settings_button = %SettingsButton
@onready var quit_button = %QuitButton

func _ready():
	# 게임 월드가 일시 정지되면 메뉴도 정지되지 않도록 처리합니다.
	process_mode = Node.PROCESS_MODE_ALWAYS

	resume_button.pressed.connect(close_menu)
	settings_button.pressed.connect(_on_settings_pressed)
	quit_button.pressed.connect(_on_quit_to_main_menu_pressed)
	settings_menu.back_pressed.connect(_on_settings_back_pressed)

func close_menu():
	emit_signal("menu_closed")
	queue_free()

func _on_settings_pressed():
	main_menu_container.hide()
	settings_menu.show()

func _on_settings_back_pressed():
	main_menu_container.show()
	settings_menu.hide()

# ▼▼▼▼▼ 여기가 수정된 부분입니다! ▼▼▼▼▼
func _on_quit_to_main_menu_pressed():
	# 게임을 다시 진행시키고, GameManager의 저장 후 종료 함수를 호출합니다.
	get_tree().paused = false
	GameManager.save_and_exit_to_main_menu()
	emit_signal("quit_to_main_menu_pressed") 
# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
