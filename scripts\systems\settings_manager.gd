# scripts/systems/settings_manager.gd
extends Node

const SETTINGS_FILE_PATH = "user://settings.save"
const KEYMAP_FILE_PATH = "user://keymap.save"

var default_settings = {
	"audio": {
		"master_volume": 80.0,
		"music_volume": 100.0,
		"sfx_volume": 100.0,
		"ambient_volume": 100.0
	},
	"video": {
		# [수정] fullscreen과 resolution을 window_mode로 변경 (0: 창, 1: 테두리 없는, 2: 전체)
		"window_mode": 0, 
		"vsync": true,
		"fps_limit": 60
	},
	"gameplay": {
		"language": "en"
	}
}
var current_settings: Dictionary

var remappable_actions = [
	"move_up", "move_down", "move_left", "move_right", "toggle_stats_panel", "toggle_worldmap"
]
var default_keymap = {}

func _ready():
	for action in remappable_actions:
		var events = InputMap.action_get_events(action)
		if not events.is_empty(): default_keymap[action] = events
	load_settings()
	load_keymap_and_apply()
	apply_all_settings()

func apply_all_settings():
	apply_gameplay_settings()
	apply_video_settings()
	apply_audio_settings()

func apply_gameplay_settings():
	var lang_code = current_settings.get("gameplay", {}).get("language", "en")
	TranslationServer.set_locale(lang_code)

# [수정] 비디오 설정 적용 로직 전체 변경
func apply_video_settings():
	var video_settings = current_settings.get("video", {})

	# Window Mode
	var window_mode = video_settings.get("window_mode", 0)
	match window_mode:
		0: # 창 모드 (Windowed)
			DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
		1: # 테두리 없는 전체 화면 (Borderless Fullscreen)
			DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
		2: # 전체 화면 (Exclusive Fullscreen)
			DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_EXCLUSIVE_FULLSCREEN)

	# VSync
	var vsync_mode = DisplayServer.VSYNC_ENABLED if video_settings.get("vsync", true) else DisplayServer.VSYNC_DISABLED
	DisplayServer.window_set_vsync_mode(vsync_mode)

	# FPS Limit
	Engine.max_fps = video_settings.get("fps_limit", 60)


func apply_audio_settings():
	var audio_settings = current_settings.get("audio", {})
	set_bus_volume("Master", audio_settings.get("master_volume", 80.0))
	set_bus_volume("Music", audio_settings.get("music_volume", 100.0))
	set_bus_volume("SFX", audio_settings.get("sfx_volume", 100.0))
	set_bus_volume("Ambient", audio_settings.get("ambient_volume", 100.0))

func set_bus_volume(bus_name: String, value: float):
	var bus_index = AudioServer.get_bus_index(bus_name)
	if bus_index != -1:
		if value > 0:
			AudioServer.set_bus_volume_db(bus_index, linear_to_db(value / 100.0))
		else:
			AudioServer.set_bus_volume_db(bus_index, -80)

# ( ... 이하 save/load 함수들은 이전과 동일 ... )
func save_settings():
	var file = FileAccess.open(SETTINGS_FILE_PATH, FileAccess.WRITE)
	if file: file.store_string(JSON.stringify(current_settings))

func load_settings():
	if not FileAccess.file_exists(SETTINGS_FILE_PATH):
		current_settings = default_settings.duplicate(true)
		save_settings()
		return
	var file = FileAccess.open(SETTINGS_FILE_PATH, FileAccess.READ)
	if file:
		var data = JSON.parse_string(file.get_as_text())
		if data: current_settings = data
		else: current_settings = default_settings.duplicate(true)

func save_keymap():
	var keymap_to_save = {}
	for action in remappable_actions:
		var keycodes = []
		for event in InputMap.action_get_events(action):
			if event is InputEventKey: keycodes.append(event.physical_keycode)
		keymap_to_save[action] = keycodes
	var file = FileAccess.open(KEYMAP_FILE_PATH, FileAccess.WRITE)
	if file: file.store_string(JSON.stringify(keymap_to_save))

func load_keymap_and_apply():
	if not FileAccess.file_exists(KEYMAP_FILE_PATH): return
	var file = FileAccess.open(KEYMAP_FILE_PATH, FileAccess.READ)
	if file:
		var text = file.get_as_text()
		if text.is_empty(): return
		var data = JSON.parse_string(text)
		if data and typeof(data) == TYPE_DICTIONARY:
			for action in data:
				if not action in remappable_actions: continue
				InputMap.action_erase_events(action)
				for keycode in data[action]:
					var new_event = InputEventKey.new()
					new_event.physical_keycode = keycode
					InputMap.action_add_event(action, new_event)

func reset_keymap():
	for action in default_keymap:
		InputMap.action_erase_events(action)
		for event in default_keymap[action]:
			InputMap.action_add_event(action, event)
	save_keymap()
