extends Control

# 부모에게 보낼 신호들을 정의합니다.
signal world_created
signal back_pressed

# --- UI 노드들을 '%' (고유 이름)으로 연결합니다. ---
@onready var name_edit = %NameEdit
@onready var create_button = %CreateButton
@onready var back_button = %BackButton
# ----------------------------------------------------

func _ready():
	# --- 버튼 신호를 코드에서 직접 연결합니다. ---
	create_button.pressed.connect(_on_create_pressed)
	back_button.pressed.connect(_on_back_pressed)
	# ---------------------------------------------

# 'Create' 버튼이 눌렸을 때 호출됩니다.
func _on_create_pressed():
	var world_name = name_edit.text
	
	if world_name.strip_edges().is_empty():
		# 참고: print()는 개발자용 디버그 메시지이므로 번역하지 않습니다.
		print("World name cannot be empty.")
		return

	var saved_worlds = SaveManager.get_saved_world_list()
	if saved_worlds.has(world_name):
		print("A world with that name already exists.")
		return

	# 세계 이름을 GameManager에 설정하고 world_created 신호 발송
	GameManager.current_world_name = world_name
	emit_signal("world_created")

# 'Back' 버튼이 눌렸을 때
func _on_back_pressed():
	emit_signal("back_pressed")
