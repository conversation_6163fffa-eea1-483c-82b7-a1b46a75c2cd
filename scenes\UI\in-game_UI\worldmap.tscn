[gd_scene load_steps=4 format=3 uid="uid://ds1dxbxijlaji"]

[ext_resource type="Script" uid="uid://dl22wuvng0qmy" path="res://scenes/UI/in-game_UI/worldmap.gd" id="1_l0ei5"]
[ext_resource type="Script" uid="uid://blj0j1v0w36i8" path="res://scenes/UI/in-game_UI/HUD/MapInteractionHandler.gd" id="2_su8g7"]
[ext_resource type="PackedScene" uid="uid://yc071iw5658w" path="res://scenes/UI/in-game_UI/HUD/map_view.tscn" id="3_wlj8w"]

[node name="Worldmap" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_l0ei5")

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = 4

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.588235)
script = ExtResource("2_su8g7")

[node name="MapView" parent="CanvasLayer/ColorRect" instance=ExtResource("3_wlj8w")]
unique_name_in_owner = true
clip_contents = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -300.0
offset_right = 300.0
offset_bottom = 300.0
mouse_filter = 2

[node name="MarginContainer" type="MarginContainer" parent="CanvasLayer"]
offset_right = 40.0
offset_bottom = 40.0

[node name="VBoxContainer" type="VBoxContainer" parent="CanvasLayer/MarginContainer"]
layout_mode = 2

[node name="PositionLabel" type="Label" parent="CanvasLayer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="BiomeLabel" type="Label" parent="CanvasLayer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
