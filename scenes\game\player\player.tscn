[gd_scene load_steps=20 format=3 uid="uid://c12e8uyk8ifmk"]

[ext_resource type="Script" uid="uid://r6c0e4eeft26" path="res://scenes/game/player/player.gd" id="1_8afob"]
[ext_resource type="Script" uid="uid://2q563nv6851e" path="res://scripts/characters/character_equipment.gd" id="2_dovo2"]
[ext_resource type="AnimationLibrary" uid="uid://cow3yjuvcjtem" path="res://assets/animation/female_animations.tres" id="2_fm80t"]
[ext_resource type="Script" uid="uid://biwelpxhjifv0" path="res://scripts/systems/health_system.gd" id="2_lvxji"]
[ext_resource type="Script" uid="uid://bgd2ldners3mq" path="res://scripts/characters/character_appearance.gd" id="3_gmlin"]
[ext_resource type="AnimationLibrary" uid="uid://1ohj1ut5orkw" path="res://assets/animation/male_animations.tres" id="3_gx1jg"]
[ext_resource type="Texture2D" uid="uid://b8dci17trw0ev" path="res://assets/graphic/character/naked/female/female_front_head.png" id="5_gx1jg"]
[ext_resource type="Texture2D" uid="uid://cir4y7w0a7nv5" path="res://assets/graphic/character/naked/female/female_front_torso.png" id="5_jvpd5"]
[ext_resource type="Texture2D" uid="uid://dcvfmfid8rhpt" path="res://assets/graphic/character/naked/female/female_front_armR.png" id="6_ugbui"]
[ext_resource type="Texture2D" uid="uid://ky84efmd6dh7" path="res://assets/graphic/character/naked/female/female_front_armL.png" id="7_fcs02"]
[ext_resource type="Texture2D" uid="uid://bchldw6bfothl" path="res://assets/graphic/character/naked/female/female_front_legR.png" id="8_myrg7"]
[ext_resource type="Texture2D" uid="uid://b6k3oopmys7f7" path="res://assets/graphic/character/naked/female/female_front_legL.png" id="9_kvlxm"]
[ext_resource type="Script" uid="uid://cj0mewab5qbqi" path="res://scripts/systems/stat_system.gd" id="13_ixkur"]
[ext_resource type="Script" uid="uid://c5v5hfv5op7jp" path="res://scripts/systems/mana_system.gd" id="13_jvpd5"]
[ext_resource type="Resource" uid="uid://w4p3aaqo4fb5" path="res://data/stats/player_base_stats.tres" id="14_jvpd5"]

[sub_resource type="Animation" id="Animation_uf5tr"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Visuals/head:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("5_gx1jg")]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Visuals/torso:texture")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("5_jvpd5")]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Visuals/armR:texture")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("6_ugbui")]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Visuals/armL:texture")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("7_fcs02")]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Visuals/legR:texture")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("8_myrg7")]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Visuals/legL:texture")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("9_kvlxm")]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_lgqa7"]
_data = {
&"RESET": SubResource("Animation_uf5tr")
}

[sub_resource type="SkeletonModificationStack2D" id="SkeletonModificationStack2D_p47bc"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_kvlxm"]
size = Vector2(20, 44)

[node name="Player" type="CharacterBody2D" node_paths=PackedStringArray("visuals") groups=["player"]]
z_index = 10
script = ExtResource("1_8afob")
visuals = NodePath("Visuals")
male_anim_library = ExtResource("3_gx1jg")
female_anim_library = ExtResource("2_fm80t")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
unique_name_in_owner = true
libraries = {
&"": SubResource("AnimationLibrary_lgqa7"),
&"female_animations": ExtResource("2_fm80t"),
&"male_animations": ExtResource("3_gx1jg")
}
speed_scale = 2.0

[node name="Visuals" type="Skeleton2D" parent="."]
position = Vector2(2.66454e-15, 8)
modification_stack = SubResource("SkeletonModificationStack2D_p47bc")

[node name="head" type="Sprite2D" parent="Visuals"]
texture = ExtResource("5_gx1jg")

[node name="torso" type="Sprite2D" parent="Visuals"]
texture = ExtResource("5_jvpd5")

[node name="armR" type="Sprite2D" parent="Visuals"]
texture = ExtResource("6_ugbui")

[node name="armL" type="Sprite2D" parent="Visuals"]
texture = ExtResource("7_fcs02")

[node name="legR" type="Sprite2D" parent="Visuals"]
texture = ExtResource("8_myrg7")

[node name="legL" type="Sprite2D" parent="Visuals"]
texture = ExtResource("9_kvlxm")

[node name="Camera2D" type="Camera2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource("RectangleShape2D_kvlxm")

[node name="VisionTimer" type="Timer" parent="."]
wait_time = 0.2
autostart = true

[node name="stat_system" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("13_ixkur")
base_stats = ExtResource("14_jvpd5")

[node name="health_system" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("2_lvxji")

[node name="mana_system" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("13_jvpd5")

[node name="character_equipment" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("2_dovo2")

[node name="character_appearance" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("3_gmlin")
