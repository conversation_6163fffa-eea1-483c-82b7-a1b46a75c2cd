# scripts/systems/stat_system.gd
extends Node
class_name StatSystem

# --- 신호 정의 ---
signal current_max_health_changed(new_value)
signal current_max_mana_changed(new_value)
# (필요에 따라 다른 스탯 변경 신호도 추가할 수 있습니다)
# signal current_attack_power_changed(new_value)


@export var base_stats: CharacterStats

# --- 현재 적용된 최종 스탯 (Setter 추가) ---
var current_max_health: float:
	set(value):
		if current_max_health != value:
			current_max_health = value
			current_max_health_changed.emit(current_max_health)

var current_max_mana: float:
	set(value):
		if current_max_mana != value:
			current_max_mana = value
			current_max_mana_changed.emit(current_max_mana)

# (다른 스탯들은 지금 당장 신호가 필요 없으므로 Setter를 추가하지 않았습니다)
var current_attack_power: float
var current_attack_speed: float
var current_cast_speed: float
var current_crit_chance: float
var current_crit_damage: float
var current_defense: float
var current_health_regen: float
var current_mana_regen: float
var current_move_speed: float


func _ready():
	if base_stats:
		# Setter를 통해 신호가 발생하도록 self를 사용하여 초기화합니다.
		self.current_max_health = base_stats.max_health
		self.current_max_mana = base_stats.max_mana
		
		# 나머지 스탯 초기화
		current_attack_power = base_stats.attack_power
		current_attack_speed = base_stats.attack_speed
		current_cast_speed = base_stats.cast_speed
		current_crit_chance = base_stats.crit_chance
		current_crit_damage = base_stats.crit_damage
		current_defense = base_stats.defense
		current_health_regen = base_stats.health_regen_per_tick
		current_mana_regen = base_stats.mana_regen_per_tick
		current_move_speed = base_stats.move_speed

# (향후 장비나 버프 효과를 적용할 함수를 여기에 추가할 수 있습니다)
# func apply_stat_modifiers():
#	  var final_health = base_stats.max_health + ...
#	  self.current_max_health = final_health # Setter가 호출되어 신호를 보냄
