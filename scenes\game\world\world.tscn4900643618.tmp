[gd_scene load_steps=5 format=3 uid="uid://b5bmj40kpunn2"]

[ext_resource type="Script" uid="uid://cyln3elt0lmd4" path="res://scenes/game/world/world.gd" id="1_ojlmk"]
[ext_resource type="TileSet" uid="uid://crc1w5xqgdd8q" path="res://assets/graphic/world/tiles/world_tileset.tres" id="1_sl2e5"]
[ext_resource type="Script" uid="uid://c5pochlblxma4" path="res://scenes/game/world/map_generator.gd" id="2_1fp7r"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_ojlmk"]

[node name="World" type="Node2D" groups=["world"]]
script = ExtResource("1_ojlmk")

[node name="MapGenerator" type="TileMapLayer" parent="." groups=["map_generator"]]
unique_name_in_owner = true
use_parent_material = true
tile_set = ExtResource("1_sl2e5")
script = ExtResource("2_1fp7r")
noise = SubResource("FastNoiseLite_ojlmk")

[node name="PlayerStartPosition" type="Marker2D" parent="."]
