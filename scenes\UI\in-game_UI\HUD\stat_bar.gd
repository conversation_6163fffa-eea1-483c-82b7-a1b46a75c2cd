# scenes/UI/in-game_UI/HUD/stat_bar.gd
extends Control

# --- 노드 참조 ---
@onready var bar_fill: TextureProgressBar = $BarFill
@onready var value_label: Label = $ValueLabel

# --- 외부 설정 변수 ---
@export var bar_color: Color = Color.WHITE


func _ready():
	bar_fill.tint_progress = bar_color


# --- 함수 ---

# [수정] 스탯 바를 '현재값'과 '최대값'으로 초기화하는 함수
func initialize(current_value: float, max_value: float):
	bar_fill.max_value = max_value
	update_bar(current_value) # update_bar가 나머지 처리를 모두 해줌


# [추가] 최대값만 업데이트하는 함수 (오류의 원인)
func update_max_value(new_max_value: float):
	bar_fill.max_value = new_max_value
	# 최대값이 바뀌었으므로 텍스트 표시도 갱신해야 함
	update_bar(bar_fill.value)


# 스탯 바의 현재값을 업데이트하고, 텍스트를 갱신합니다.
func update_bar(current_value: float):
	# TextureProgressBar의 값을 업데이트합니다.
	bar_fill.value = current_value
	
	# Label의 텍스트를 "현재값 / 최대값" 형식으로 업데이트합니다.
	value_label.text = "%d / %d" % [current_value, bar_fill.max_value]
