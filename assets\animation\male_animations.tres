[gd_resource type="AnimationLibrary" load_steps=14 format=3 uid="uid://1ohj1ut5orkw"]

[ext_resource type="Texture2D" uid="uid://b1fi712v8s5gb" path="res://assets/graphic/character/naked/male/male_front_legL.png" id="1_pth1t"]
[ext_resource type="Texture2D" uid="uid://smt6wljcxlb1" path="res://assets/graphic/character/naked/male/male_front_legR.png" id="2_rdwmt"]
[ext_resource type="Texture2D" uid="uid://dc486bhe31b3r" path="res://assets/graphic/character/naked/male/male_front_armL.png" id="3_11xbo"]
[ext_resource type="Texture2D" uid="uid://cntrphbr3hdcj" path="res://assets/graphic/character/naked/male/male_front_armR.png" id="4_3cme8"]
[ext_resource type="Texture2D" uid="uid://bv7rq40ghqh56" path="res://assets/graphic/character/naked/male/male_front_head.png" id="5_4kn03"]
[ext_resource type="Texture2D" uid="uid://78uf4mdmi16g" path="res://assets/graphic/character/naked/male/male_front_torso.png" id="6_aqi50"]

[sub_resource type="Animation" id="Animation_gmlin"]
resource_name = "RESET"
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Visuals/head:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("5_4kn03")]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Visuals/torso:texture")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("6_aqi50")]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Visuals/armR:texture")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("4_3cme8")]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Visuals/armL:texture")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("3_11xbo")]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Visuals/legR:texture")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("2_rdwmt")]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Visuals/legL:texture")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("1_pth1t")]
}

[sub_resource type="Animation" id="Animation_myrg7"]
resource_name = "idle_down"
loop_mode = 1

[sub_resource type="Animation" id="Animation_bbmj2"]
resource_name = "idle_side"
loop_mode = 1

[sub_resource type="Animation" id="Animation_4hvnv"]
resource_name = "idle_up"
loop_mode = 1

[sub_resource type="Animation" id="Animation_dovo2"]
resource_name = "walk_down"
loop_mode = 1

[sub_resource type="Animation" id="Animation_yj68g"]
resource_name = "walk_side"
length = 1.33334
loop_mode = 1

[sub_resource type="Animation" id="Animation_ioxgp"]
resource_name = "walk_up"
loop_mode = 1

[resource]
_data = {
&"RESET": SubResource("Animation_gmlin"),
&"idle_down": SubResource("Animation_myrg7"),
&"idle_side": SubResource("Animation_bbmj2"),
&"idle_up": SubResource("Animation_4hvnv"),
&"walk_down": SubResource("Animation_dovo2"),
&"walk_side": SubResource("Animation_yj68g"),
&"walk_up": SubResource("Animation_ioxgp")
}
