extends Control

@export var map_colors: Resource

@onready var player: Node2D = get_tree().get_first_node_in_group("player")

# fog_image 변수는 이제 MapView에서 직접 수정하지 않으므로 없어도 됩니다.
# var fog_image: Image 

@onready var map_background: TextureRect = $MapBackground
@onready var fog_overlay: TextureRect = $FogOverlay
@onready var player_icon: TextureRect = $PlayerIcon

func _ready():
	MapDataManager.map_data_updated.connect(display_map)
	display_map()
	
	# ExplorationManager의 신호를 받으면 MapDataManager에 업데이트를 요청합니다.
	ExplorationManager.map_exploration_updated.connect(_on_exploration_updated)
	
	# [추가] MapDataManager의 안개 업데이트 신호를 받으면 화면을 갱신합니다.
	MapDataManager.map_fog_updated.connect(_on_map_fog_updated)

func _process(_delta):
	if not is_instance_valid(player):
		player = get_tree().get_first_node_in_group("player")
		if not is_instance_valid(player):
			return
	update_player_icon_position()

func display_map():
	if is_instance_valid(MapDataManager.cached_map_texture):
		map_background.texture = MapDataManager.cached_map_texture
		fog_overlay.texture = MapDataManager.cached_fog_texture
	else:
		MapDataManager.map_cache_generated.connect(_on_map_cache_ready, CONNECT_ONE_SHOT)

func _on_map_cache_ready():
	if is_instance_valid(MapDataManager.cached_map_texture):
		map_background.texture = MapDataManager.cached_map_texture
		fog_overlay.texture = MapDataManager.cached_fog_texture

# [수정] 이 함수는 이제 MapDataManager에 작업을 위임합니다.
func _on_exploration_updated(newly_revealed_tiles: Array):
	MapDataManager.update_fog_of_war(newly_revealed_tiles)

# [추가] MapDataManager가 안개를 업데이트했을 때 호출되는 함수
func _on_map_fog_updated():
	# 데이터 관리자로부터 최신 안개 텍스처를 가져와 UI에 적용하기만 합니다.
	fog_overlay.texture = MapDataManager.cached_fog_texture

func update_player_icon_position():
	if not is_instance_valid(player): return
	var map_info = MapDataManager.get_current_map_info()
	if not map_info or not map_background.texture: return
	var tile_size = MapDataManager.tile_size
	if tile_size == 0: return
	var player_pos_in_tiles = player.global_position / float(tile_size)
	var map_origin_in_tiles = Vector2(map_info.get("origin", Vector2i.ZERO))
	var player_pos_on_texture = player_pos_in_tiles - map_origin_in_tiles
	var map_texture_size = map_background.texture.get_size()
	if map_texture_size.x == 0 or map_texture_size.y == 0: return
	var scale_ratio = map_background.size / map_texture_size
	player_icon.position = player_pos_on_texture * scale_ratio - (player_icon.size / 2)
