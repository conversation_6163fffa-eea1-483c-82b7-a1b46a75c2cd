# scenes/UI/in-game_UI/character_stats_panel.gd
extends Control

# --- 이름 레이블들을 변수로 연결 ---
@onready var health_name_label = %HealthNameLabel
@onready var mana_name_label = %ManaNameLabel
@onready var attack_damage_name_label = %AttackDamageNameLabel
@onready var armor_name_label = %ArmorNameLabel
@onready var movement_speed_name_label = %MovementSpeedNameLabel

# --- 값 레이블들을 변수로 연결 ---
@onready var health_value_label = %HealthValueLabel
@onready var mana_value_label = %ManaValueLabel
@onready var attack_damage_value_label = %AttackDamageValueLabel
@onready var armor_value_label = %ArmorValueLabel
@onready var movement_speed_value_label = %MovementSpeedValueLabel

# 패널이 처음 준비될 때 스탯 이름에 번역을 적용합니다.
func _ready():
	health_name_label.text = tr("STAT_HEALTH")
	mana_name_label.text = tr("STAT_MANA")
	attack_damage_name_label.text = tr("STAT_ATTACK_DAMAGE")
	armor_name_label.text = tr("STAT_ARMOR")
	movement_speed_name_label.text = tr("STAT_MOVEMENT_SPEED")

# [수정] StatSystem에 정의된 올바른 변수 이름으로 수정했습니다.
func update_stats(player_stat_system: StatSystem, player_health_system: HealthSystem, player_mana_system: ManaSystem):
	if not player_stat_system or not player_health_system or not player_mana_system:
		printerr("Stats panel update failed: one of the systems is null.")
		return

	health_value_label.text = "%s / %s" % [snapped(player_health_system.current_health, 0.01), player_stat_system.current_max_health]
	mana_value_label.text = "%s / %s" % [snapped(player_mana_system.current_mana, 0.01), player_stat_system.current_max_mana]

	# [오류 수정] 올바른 변수 이름으로 변경
	attack_damage_value_label.text = str(player_stat_system.current_attack_power)
	armor_value_label.text = str(player_stat_system.current_defense)
	movement_speed_value_label.text = str(player_stat_system.current_move_speed)
