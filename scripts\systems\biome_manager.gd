# scripts/systems/biome_manager.gd
extends Node

@export var biomes: Array[BiomeData]

# ... (다른 변수 및 함수들은 이전과 동일하게 유지) ...
var temperature_noise: FastNoiseLite
var humidity_noise: FastNoiseLite
var continentalness_noise: FastNoiseLite
var warp_noise: FastNoiseLite
var lake_noise: FastNoiseLite 
var is_initialized = false

func initialize_with_seed(world_seed_string: String):
	var base_seed = world_seed_string.hash()
	temperature_noise = create_noise(base_seed)
	humidity_noise = create_noise(base_seed + 1)
	continentalness_noise = create_noise_continental(base_seed + 2)
	warp_noise = create_noise_warp(base_seed + 3)
	lake_noise = create_noise_lake(base_seed + 4)
	is_initialized = true

func create_noise(p_seed: int) -> FastNoiseLite:
	var noise = FastNoiseLite.new()
	noise.seed = p_seed
	noise.noise_type = FastNoiseLite.TYPE_PERLIN
	noise.frequency = 0.002
	noise.fractal_octaves = 4
	noise.fractal_lacunarity = 2.0
	noise.fractal_gain = 0.5
	return noise

func create_noise_continental(p_seed: int) -> FastNoiseLite:
	var noise = FastNoiseLite.new()
	noise.seed = p_seed
	noise.noise_type = FastNoiseLite.TYPE_SIMPLEX
	noise.frequency = 0.003
	noise.fractal_type = FastNoiseLite.FRACTAL_RIDGED
	noise.fractal_octaves = 5
	noise.fractal_lacunarity = 2.0
	noise.fractal_gain = 0.5
	return noise

func create_noise_warp(p_seed: int) -> FastNoiseLite:
	var noise = FastNoiseLite.new()
	noise.seed = p_seed
	noise.noise_type = FastNoiseLite.TYPE_PERLIN
	noise.frequency = 0.004
	noise.fractal_octaves = 5
	return noise

func create_noise_lake(p_seed: int) -> FastNoiseLite:
	var noise = FastNoiseLite.new()
	noise.seed = p_seed
	noise.noise_type = FastNoiseLite.TYPE_SIMPLEX
	noise.frequency = 0.003
	noise.fractal_type = FastNoiseLite.FRACTAL_FBM
	noise.fractal_octaves = 2
	return noise
# --- 여기까지 이전 코드와 동일 ---

func get_biome(world_pos: Vector2i) -> BiomeData:
	if not is_initialized:
		printerr("BiomeManager has not been initialized with a seed!")
		return biomes[0] if not biomes.is_empty() else null

	# 1. 대륙 생성 및 높이 계산 (변경 없음)
	var warp_strength = 60.0
	var warped_x = world_pos.x + (warp_noise.get_noise_2d(float(world_pos.x), float(world_pos.y)) * warp_strength)
	var warped_y = world_pos.y + (warp_noise.get_noise_2d(float(world_pos.x + 543.2), float(world_pos.y - 123.4)) * warp_strength)
	var warped_pos = Vector2(warped_x, warped_y)
	var noise_value = continentalness_noise.get_noise_2d(world_pos.x, world_pos.y)
	var heightmap_base = (noise_value + 1.0) / 2.0
	
	var continent_radius = 300.0
	var distance_from_center = warped_pos.length()
	var gradient = 1.0 - clamp(distance_from_center / continent_radius, 0.0, 1.0)
	var final_height = heightmap_base * gradient
	
	# 2. 높이에 따른 바이옴 결정
	var sea_level = 0.1
	if final_height < sea_level:
		for biome in biomes:
			if biome.name == "Ocean":
				return biome
		printerr("Ocean biome not found!")
		return null

	# 3. [핵심 수정] 해안선 완충 지대 설정
	# sea_level 바로 위의 특정 높이 구간을 '해안가'로 정의하고, 이 구역에서는 호수 생성을 건너뜁니다.
	var coastal_zone_threshold = 0.12 # sea_level보다 약간 높은 값으로 설정
	if final_height >= coastal_zone_threshold:
		# 충분히 내륙으로 들어온 지역에서만 호수 생성 로직을 실행
		var lake_threshold = 0.75
		var lake_noise_value = (lake_noise.get_noise_2d(world_pos.x, world_pos.y) + 1.0) / 2.0
		
		if lake_noise_value > lake_threshold:
			for biome in biomes:
				if biome.name == "Lake":
					return biome
	
	# 4. 해안가 및 일반 육지 바이옴 결정
	var temp_noise = temperature_noise.get_noise_2d(world_pos.x, world_pos.y)
	var hum_noise = humidity_noise.get_noise_2d(world_pos.x, world_pos.y)
	var best_biome: BiomeData = null
	var smallest_distance = INF
	for biome in biomes:
		if biome.name == "Ocean" or biome.name == "Lake": continue
		
		var temp_diff = abs(temp_noise - biome.optimal_temperature)
		var hum_diff = abs(hum_noise - biome.optimal_humidity)
		var distance = temp_diff + hum_diff
		if distance < smallest_distance:
			smallest_distance = distance
			best_biome = biome
	
	return best_biome

# ... (get_temperature 함수는 변경 없음) ...
func get_temperature(world_pos: Vector2i) -> float:
	if not is_initialized:
		printerr("BiomeManager has not been initialized with a seed!")
		return 0.0
	var noise_value = temperature_noise.get_noise_2d(world_pos.x, world_pos.y)
	var temperature = (noise_value * 30.0) + 0.0
	return temperature
