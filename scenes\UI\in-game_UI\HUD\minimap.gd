# minimap.gd
extends Control

# --- 노드 참조 ---
@onready var map_view: Control = %MapView
@onready var zoom_in_button: Button = %ZoomInButton
@onready var zoom_out_button: Button = %ZoomOutButton

# --- 줌 설정 ---
@export var min_zoom := 2.0
@export var max_zoom := 4.8
@export var zoom_step := 0.4

# --- 내부 변수 ---
var current_zoom_level: float = 3.2

func _ready():
	zoom_in_button.pressed.connect(_on_zoom_in_pressed)
	zoom_out_button.pressed.connect(_on_zoom_out_pressed)
	apply_zoom()

func _process(_delta):
	# 이 로직은 MapView가 올바르게 초기화되고 아이콘 위치를 계산한다는 전제 하에 정확하게 동작합니다.
	if not is_instance_valid(map_view) or not is_instance_valid(map_view.player_icon) or not map_view.player_icon.visible:
		return

	var minimap_center = size / 2.0
	var player_icon_center_in_mapview = map_view.player_icon.position + (map_view.player_icon.size / 2.0)
	var scaled_icon_position = player_icon_center_in_mapview * map_view.scale
	
	map_view.position = minimap_center - scaled_icon_position


# --- 줌 함수 ---
func _on_zoom_in_pressed():
	current_zoom_level += zoom_step
	apply_zoom()

func _on_zoom_out_pressed():
	current_zoom_level -= zoom_step
	apply_zoom()

func apply_zoom():
	current_zoom_level = clamp(current_zoom_level, min_zoom, max_zoom)
	if is_instance_valid(map_view):
		map_view.scale = Vector2(current_zoom_level, current_zoom_level)
