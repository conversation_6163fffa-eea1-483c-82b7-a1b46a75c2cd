# editor/map_color_baker.gd (최종 완성 버전)
@tool
extends EditorScript

func _run():
	print("미니맵 색상 데이터 베이킹 시작...")

	var MapColorDataScript = load("res://scripts/data_definitions/map_color_data.gd")
	if not MapColorDataScript:
		print("ERROR: 'MapColorData.gd' 스크립트를 찾을 수 없습니다!")
		return

	var tile_set: TileSet = load("res://assets/graphic/world/tiles/world_tileset.tres")
	if not tile_set:
		print("타일셋을 찾을 수 없습니다!")
		return

	var color_data = MapColorDataScript.new()

	for source_id in range(tile_set.get_source_count()):
		var source = tile_set.get_source(source_id)
		if not source is TileSetAtlasSource:
			continue
			
		var texture = source.texture
		if not texture: continue
		
		var image = texture.get_image()
		if not image: continue
			
		if source.get_tiles_count() == 0:
			print("경고: Source ID ", source_id, " 에는 타일이 하나도 없습니다. 건너뜁니다.")
			continue
		
		var atlas_coords = source.get_tile_id(0)
		var rect = source.get_tile_texture_region(atlas_coords)
		
		var total_r = 0.0
		var total_g = 0.0
		var total_b = 0.0
		var pixel_count = 0

		for img_y in range(rect.position.y, rect.position.y + rect.size.y):
			for img_x in range(rect.position.x, rect.position.x + rect.size.x):
				var pixel_color = image.get_pixel(img_x, img_y)
				if pixel_color.a > 0.1:
					total_r += pixel_color.r
					total_g += pixel_color.g
					total_b += pixel_color.b
					pixel_count += 1
		
		if pixel_count > 0:
			var avg_color = Color(total_r / pixel_count, total_g / pixel_count, total_b / pixel_count)
			color_data.color_map[source_id] = avg_color
			print("계산 완료: ID ", source_id, " -> ", avg_color)

	var save_path = "res://data/map_color_data.tres"
	var error = ResourceSaver.save(color_data, save_path)

	if error == OK:
		print("성공! 미니맵 색상 데이터가 '", save_path, "'에 저장되었습니다.")
		
		# ▼▼▼▼▼ 여기가 최종 핵심 해결책입니다! ▼▼▼▼▼
		# 에디터 파일 시스템을 업데이트하여 변경사항을 즉시 반영하도록 합니다.
		if Engine.is_editor_hint():
			var editor_fs = EditorInterface.get_resource_filesystem()
			editor_fs.scan()
		# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
			
	else:
		print("저장 실패! 오류 코드: ", error)
