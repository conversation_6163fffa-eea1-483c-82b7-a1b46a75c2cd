# scenes/UI/character_selection/character_creation.gd
extends Control

signal character_created
signal back_pressed

@onready var name_edit = %NameEdit
@onready var male_button = %MaleButton
@onready var female_button = %FemaleButton
@onready var create_button = %CreateButton
@onready var back_button = %BackButton

var selected_gender = "male"

func _ready():
	male_button.pressed.connect(_on_male_button_pressed)
	female_button.pressed.connect(_on_female_button_pressed)
	create_button.pressed.connect(_on_create_button_pressed)
	back_button.pressed.connect(_on_back_pressed)
	
	name_edit.placeholder_text = tr("CHARACTER_ENTER_NAME")
	_on_male_button_pressed()

func _on_male_button_pressed():
	selected_gender = "male"
	male_button.disabled = true
	female_button.disabled = false

func _on_female_button_pressed():
	selected_gender = "female"
	male_button.disabled = false
	female_button.disabled = true

func _on_create_button_pressed():
	var char_name = name_edit.text.strip_edges()
	if char_name.is_empty():
		print("Character name cannot be empty.")
		return

	var existing_character_names = SaveManager.get_saved_character_list()
	if char_name in existing_character_names:
		print("Character with that name already exists.")
		return
	
	var new_char_data = {
		"name": char_name,
		"gender": selected_gender,
		"exploration_data": {}
	}
	
	# [오류 수정] 데이터(new_char_data)와 이름(char_name)을 함께 전달합니다.
	SaveManager.save_character_data(new_char_data, char_name)
	
	emit_signal("character_created")

func _on_back_pressed():
	emit_signal("back_pressed")
