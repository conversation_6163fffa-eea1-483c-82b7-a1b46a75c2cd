# scripts/systems/mod_manager.gd
extends Node

# 모든 모드의 미니맵 색상 정보를 저장할 딕셔너리
var map_colors: Dictionary = {}

func _ready():
	load_mod_map_data()

# "res://mods/" 폴더를 스캔하여 모든 모드의 색상 데이터를 불러옵니다.
func load_mod_map_data():
	print("모드 미니맵 데이터 로딩 시작...")
	var mods_dir = "res://mods/"
	
	# DirAccess를 사용하여 폴더를 엽니다.
	var dir = DirAccess.open(mods_dir)
	if dir:
		dir.list_dir_begin()
		var dir_name = dir.get_next()
		while dir_name != "":
			# 폴더만 처리합니다.
			if dir.current_is_dir() and dir_name != "." and dir_name != "..":
				var mod_path = mods_dir.path_join(dir_name)
				var color_file_path = mod_path.path_join("map_colors.json")
				
				# 해당 모드 폴더 안에 map_colors.json 파일이 있는지 확인합니다.
				if FileAccess.file_exists(color_file_path):
					print("발견: ", color_file_path)
					var file = FileAccess.open(color_file_path, FileAccess.READ)
					var content = file.get_as_text()
					file.close()
					
					# JSON 파싱
					var json = JSON.new()
					var error = json.parse(content)
					if error == OK:
						var data = json.get_data()
						# 읽어온 데이터를 메인 딕셔너리에 합칩니다.
						map_colors.merge(data, true)
					else:
						print("JSON 파싱 오류: ", color_file_path, " - ", json.get_error_message())
						
			dir_name = dir.get_next()
	else:
		print("'res://mods/' 폴더를 찾을 수 없습니다.")

	print("모드 미니맵 데이터 로딩 완료.")
