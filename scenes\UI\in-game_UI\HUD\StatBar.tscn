[gd_scene load_steps=4 format=3 uid="uid://hej54n1wkswk"]

[ext_resource type="Script" uid="uid://cydse34cwf8ys" path="res://scenes/UI/in-game_UI/HUD/stat_bar.gd" id="1_yux3j"]
[ext_resource type="Texture2D" uid="uid://dnu4k1ftwa8vr" path="res://assets/graphic/UI/HUD/StatBar_background.png" id="2_yux3j"]
[ext_resource type="Texture2D" uid="uid://ckwe5s3mes2u4" path="res://assets/graphic/UI/HUD/StatBar_fill.png" id="3_u1rwr"]

[node name="StatBar" type="Control"]
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_yux3j")

[node name="BarBackground" type="TextureProgressBar" parent="."]
layout_mode = 1
offset_right = 40.0
offset_bottom = 40.0
texture_under = ExtResource("2_yux3j")

[node name="BarFill" type="TextureProgressBar" parent="."]
layout_mode = 1
offset_left = 24.0
offset_top = 20.0
offset_right = 473.0
offset_bottom = 67.0
texture_progress = ExtResource("3_u1rwr")

[node name="ValueLabel" type="Label" parent="."]
layout_mode = 1
offset_right = 72.0
offset_bottom = 16.0
