extends Node

# 장비가 변경되었을 때 다른 노드에 알리기 위한 시그널
# equipment_data: 장착/해제된 아이템 데이터, slot: 장비 부위(예: "helmet")
signal equipment_changed(equipment_data, slot, is_equipped)

# 현재 장착된 장비를 저장하는 딕셔너리
var equipped_items = {
	"helmet": null,
	"armor": null,
	"weapon": null
	# 필요한 다른 장비 부위 추가
}

# 장비를 장착하는 함수
func equip_item(item_data: EquipmentData, slot: String):
	if equipped_items.has(slot):
		# 만약 이미 해당 슬롯에 다른 아이템이 있다면 해제
		if equipped_items[slot] != null:
			unequip_item(slot)
		
		equipped_items[slot] = item_data
		equipment_changed.emit(item_data, slot, true)
		print(item_data.item_name, " 장착됨")

# 장비를 해제하는 함수
func unequip_item(slot: String):
	if equipped_items.has(slot) and equipped_items[slot] != null:
		var item_to_unequip = equipped_items[slot]
		equipped_items[slot] = null
		equipment_changed.emit(item_to_unequip, slot, false)
		print(item_to_unequip.item_name, " 해제됨")
