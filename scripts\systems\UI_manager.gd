# scripts/systems/UI_manager.gd
extends Node

signal back_in_menu_requested

const PAUSE_MENU_SCENE = preload("res://scenes/UI/pause_menu/pause_menu.tscn")
const STATS_PANEL_SCENE = preload("res://scenes/UI/in-game_UI/character_stats_panel.tscn")
const WORLD_MAP_SCENE = preload("res://scenes/UI/in-game_UI/worldmap.tscn")

var current_pause_menu = null
var current_stats_panel = null
var current_world_map = null

func _ready():
	process_mode = Node.PROCESS_MODE_ALWAYS

func _unhandled_input(event: InputEvent):
	var current_scene = get_tree().current_scene

	if current_scene and current_scene.is_in_group("game_scene"):
		
		if event.is_action_pressed("toggle_worldmap"):
			# 다른 UI가 열려있을 때는 맵을 열지 않습니다.
			if current_pause_menu or current_stats_panel:
				return
			get_viewport().set_input_as_handled()
			toggle_world_map()
		
		if event.is_action_pressed("toggle_stats_panel"):
			# 맵이 열려있을 때 스탯창이 열리지 않도록 유지
			if not current_pause_menu and not current_world_map:
				get_viewport().set_input_as_handled()
				toggle_stats_panel()
			
		if event.is_action_pressed("ui_cancel"):
			get_viewport().set_input_as_handled()
			# 맵이 열려있다면 맵을 닫는 것을 우선 처리
			if current_world_map:
				current_world_map.close_map()
			elif current_stats_panel:
				toggle_stats_panel()
			elif current_pause_menu:
				current_pause_menu.close_menu()
			else: # 최후의 수단으로 일시 정지 메뉴 열기
				get_tree().paused = true
				current_pause_menu = PAUSE_MENU_SCENE.instantiate()
				current_pause_menu.quit_to_main_menu_pressed.connect(_on_quit_to_main_menu)
				current_pause_menu.menu_closed.connect(_on_pause_menu_closed)
				get_tree().root.add_child(current_pause_menu)
	else: # 메뉴 씬일 경우
		if event.is_action_pressed("ui_cancel"):
			emit_signal("back_in_menu_requested")

# [통합] 월드맵을 열거나 닫는 토글 함수
func toggle_world_map():
	if is_instance_valid(current_world_map):
		# 맵이 이미 열려있으면 닫습니다.
		current_world_map.close_map()
	else:
		# 맵이 닫혀있으면 엽니다. (일시 정지 코드 없음)
		current_world_map = WORLD_MAP_SCENE.instantiate()
		current_world_map.map_closed.connect(_on_world_map_closed)
		get_tree().root.add_child(current_world_map)

func toggle_stats_panel():
	if current_stats_panel:
		current_stats_panel.queue_free()
		current_stats_panel = null
	else:
		var player_node = GameManager.player_node
		if player_node and STATS_PANEL_SCENE:
			current_stats_panel = STATS_PANEL_SCENE.instantiate()
			get_tree().root.add_child(current_stats_panel)
			var stat_system = player_node.get_node_or_null("%stat_system")
			var health_system = player_node.get_node_or_null("%health_system")
			var mana_system = player_node.get_node_or_null("%mana_system")
			if stat_system and health_system and mana_system:
				current_stats_panel.update_stats(stat_system, health_system, mana_system)
			else:
				printerr("UIManager Error: Could not find required systems in player node.")
		else:
			printerr("UIManager Error: Could not find player node in GameManager.")

func _on_pause_menu_closed():
	get_tree().paused = false
	current_pause_menu = null

func _on_quit_to_main_menu():
	if current_pause_menu:
		current_pause_menu.queue_free()
		current_pause_menu = null
	get_tree().change_scene_to_file("res://scenes/UI/main_menu/main_menu.tscn")

# [통합] 월드맵이 닫혔을 때 실행되는 함수 (일시 정지 해제 코드 없음)
func _on_world_map_closed():
	# 맵 인스턴스는 스스로를 파괴하므로, 여기선 참조만 제거합니다.
	current_world_map = null
