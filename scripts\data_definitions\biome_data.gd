# scripts/data_definitions/biome_data.gd
extends Resource
class_name BiomeData

## 바이옴의 이름 (예: "숲", "사막")
@export var biome_name: String = "New Biome"
@export var name: String = ""

## 바이옴 생성에 사용될 타일 목록과 가중치
@export var tile_weights: Array[TileWeightData]

## [수정] 이 바이옴이 생성되기에 가장 이상적인 최적 조건을 설정합니다.
@export_group("Generation Conditions")
# 값의 범위는 -1.0 (춥고 건조) 부터 1.0 (덥고 습함) 까지입니다.
@export var optimal_temperature: float = 0.0
@export var optimal_humidity: float = 0.0
