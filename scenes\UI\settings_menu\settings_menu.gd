# scenes/UI/settings_menu/settings_menu.gd
extends Control

signal back_pressed

@onready var window_mode_option = %WindowModeOption
@onready var vsync_check = %Vsync<PERSON><PERSON><PERSON>
@onready var master_slider = %MasterSlider
@onready var music_slider = %MusicSlider
@onready var sfx_slider = %SfxSlider
@onready var ambient_slider = %AmbientSlider
@onready var fps_option = %FpsOption
@onready var language_option = %LanguageOption
@onready var apply_button = %ApplyButton
@onready var back_button = %BackButton
@onready var controls_grid = %ControlsGrid
@onready var reset_keys_button = %ResetKeysButton

var action_to_remap = ""

var fps_limits = [30, 60, 120, 144, 0]

# [변경] 언어 이름을 다시 직접 입력하는 방식으로 복원
var languages = {
	"en": "English",
	"ko": "한국어"
}

var action_name_keys = {
	"move_up": "INPUT_MOVE_UP",
	"move_down": "INPUT_MOVE_DOWN",
	"move_left": "INPUT_MOVE_LEFT",
	"move_right": "INPUT_MOVE_RIGHT",
	"toggle_stats_panel": "INPUT_TOGGLE_STATS_PANEL",
	"toggle_worldmap": "INPUT_TOGGLE_WORLDMAP"
}

func _ready():
	apply_button.pressed.connect(_on_apply_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)
	reset_keys_button.pressed.connect(_on_reset_keys_button_pressed)
	
	populate_option_buttons()
	load_settings_to_ui()
	populate_controls_tab()

func _input(event):
	if action_to_remap.is_empty(): return
	if event is InputEventKey and event.is_pressed():
		if event.keycode == KEY_ESCAPE:
			action_to_remap = ""
			populate_controls_tab()
			return
		InputMap.action_erase_events(action_to_remap)
		InputMap.action_add_event(action_to_remap, event)
		action_to_remap = ""
		populate_controls_tab()
		get_viewport().set_input_as_handled()

func populate_controls_tab():
	for child in controls_grid.get_children(): child.queue_free()
	for action in SettingsManager.remappable_actions:
		var label = Label.new()
		label.text = tr(action_name_keys.get(action, action.capitalize()))
		
		var button = Button.new()
		var events = InputMap.action_get_events(action)
		if not events.is_empty() and events[0] is InputEventKey:
			button.text = OS.get_keycode_string(events[0].physical_keycode)
		else: 
			button.text = tr("INPUT_NOT_SET")
		
		if action_to_remap == action: button.text = "..."
		button.pressed.connect(_on_remap_button_pressed.bind(action))
		controls_grid.add_child(label)
		controls_grid.add_child(button)

func _on_remap_button_pressed(action_name):
	action_to_remap = action_name
	populate_controls_tab()

func _on_reset_keys_button_pressed():
	SettingsManager.reset_keymap()
	populate_controls_tab()

func populate_option_buttons():
	window_mode_option.add_item(tr("SETTINGS_VIDEO_MODE_WINDOWED"))
	window_mode_option.add_item(tr("SETTINGS_VIDEO_MODE_BORDERLESS"))
	window_mode_option.add_item(tr("SETTINGS_VIDEO_MODE_FULLSCREEN"))
	
	for fps in fps_limits:
		var text = tr("SETTINGS_VIDEO_FPS_UNLIMITED") if fps == 0 else str(fps)
		fps_option.add_item(text)

	# [변경] tr() 함수를 사용하지 않고 딕셔너리의 값을 그대로 사용하도록 복원
	for code in languages:
		language_option.add_item(languages[code])
		language_option.set_item_metadata(-1, code)

func load_settings_to_ui():
	var gameplay_settings = SettingsManager.current_settings.get("gameplay", {})
	var lang_code = gameplay_settings.get("language", "en")
	for i in range(language_option.item_count):
		if language_option.get_item_metadata(i) == lang_code:
			language_option.select(i); break

	var video_settings = SettingsManager.current_settings.get("video", {})
	window_mode_option.select(video_settings.get("window_mode", 0))
	vsync_check.button_pressed = video_settings.get("vsync", true)
	
	var fps_val = video_settings.get("fps_limit", 60)
	for i in range(fps_limits.size()):
		if fps_limits[i] == fps_val:
			fps_option.select(i); break

	var audio_settings = SettingsManager.current_settings.get("audio", {})
	master_slider.value = audio_settings.get("master_volume", 80.0)
	music_slider.value = audio_settings.get("music_volume", 100.0)
	sfx_slider.value = audio_settings.get("sfx_volume", 100.0)
	ambient_slider.value = audio_settings.get("ambient_volume", 100.0)

func _on_apply_button_pressed():
	var selected_lang_id = language_option.get_selected_id()
	SettingsManager.current_settings["gameplay"]["language"] = language_option.get_item_metadata(selected_lang_id)
	
	SettingsManager.current_settings["video"]["window_mode"] = window_mode_option.selected
	SettingsManager.current_settings["video"]["vsync"] = vsync_check.button_pressed
	SettingsManager.current_settings["video"]["fps_limit"] = fps_limits[fps_option.selected]
	
	SettingsManager.current_settings["audio"]["master_volume"] = master_slider.value
	SettingsManager.current_settings["audio"]["music_volume"] = music_slider.value
	SettingsManager.current_settings["audio"]["sfx_volume"] = sfx_slider.value
	SettingsManager.current_settings["audio"]["ambient_volume"] = ambient_slider.value
	
	SettingsManager.save_settings()
	SettingsManager.save_keymap()
	SettingsManager.apply_all_settings()
	
	print("Settings Applied!")

func _on_back_button_pressed():
	load_settings_to_ui()
	SettingsManager.load_keymap_and_apply()
	populate_controls_tab()
	emit_signal("back_pressed")
	hide()
