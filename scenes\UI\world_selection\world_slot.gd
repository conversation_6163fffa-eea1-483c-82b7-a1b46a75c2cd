# world_slot.gd
extends Button

# [추가] 새로운 신호들
signal double_clicked
signal delete_requested

# [수정] 필요한 노드만 변수로 연결
@onready var world_name_label: Label = %WorldNameLabel
@onready var delete_button: Button = %DeleteButton

var world_name: String

func _ready():
	world_name_label.text = world_name
	delete_button.pressed.connect(_on_delete_pressed)

# [삭제] set_world_name 함수는 더 이상 필요 없음

# [추가] 더블 클릭 감지
func _gui_input(event: InputEvent):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT and event.double_click:
		if not delete_button.is_hovered():
			emit_signal("double_clicked")
			if get_viewport():
				get_viewport().set_input_as_handled()

# [추가] 슬롯 내 삭제 버튼 처리
func _on_delete_pressed():
	emit_signal("delete_requested")
