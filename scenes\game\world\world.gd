extends Node2D

const MAP_COLORS = preload("res://data/map_color_data.tres")

@onready var map_generator: TileMapLayer = %MapGenerator

var generated_tile_types: Dictionary = {}

func _ready():
	if not is_instance_valid(MAP_COLORS):
		printerr("World scene could not load the Map Colors resource!")
		return
		
	var world_name = GameManager.current_world_name
	if world_name.is_empty():
		world_name = "default"

	# 참고: _setup_tile_definitions()는 map_generator.gd에 있어야 합니다.
	map_generator._setup_tile_definitions() 
	var loaded_data = SaveManager.load_world_data(world_name)

	if not loaded_data.is_empty() and loaded_data.has("tiles"):
		generated_tile_types = MapLoader.get_data_from_save(loaded_data["tiles"])
	else:
		generated_tile_types = map_generator.generate_world_data(world_name)
		MapLoader.save_map_data(world_name, generated_tile_types)

	_draw_map_from_data()
	
	var min_coord = Vector2i(map_generator.min_x, map_generator.min_y)
	var width = map_generator.max_x - map_generator.min_x + 1
	var height = map_generator.max_y - map_generator.min_y + 1
	var map_data_for_view = MapLoader.get_map_data_for_tilemap(generated_tile_types)
	
	MapDataManager.set_current_map_data(map_data_for_view, min_coord, width, height, MAP_COLORS)

# [핵심 수정] 타일을 그리는 로직을 새로운 tile_definitions 구조에 맞게 수정합니다.
func _draw_map_from_data():
	var tile_definitions = map_generator.tile_definitions
	
	# generated_tile_types의 키는 Vector2i이므로, 변환 없이 직접 사용합니다.
	for coord in generated_tile_types:
		var tile_type = generated_tile_types[coord] # e.g., "grass", "dirt"
		
		if tile_definitions.has(tile_type):
			var tile_info = tile_definitions[tile_type]
			
			# map_generator.gd에 정의된 source_id와 atlas_coords를 가져옵니다.
			var source_id = tile_info["source_id"]
			var atlas_coords = tile_info["atlas_coords"]
			
			# set_cell 함수에 정확한 source_id를 전달하여 올바른 타일을 그리도록 합니다.
			map_generator.set_cell(coord, source_id, atlas_coords)
		else:
			print("Tile type not found in definitions: " + tile_type)
