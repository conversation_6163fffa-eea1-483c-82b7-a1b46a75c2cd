[gd_scene load_steps=9 format=3 uid="uid://bk8m3n5qsavx"]

[ext_resource type="Script" uid="uid://d3v3o7cmb3ohe" path="res://scenes/3d_test/character_3d.gd" id="1_char3d"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_body"]
radius = 0.3
height = 1.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_body"]
albedo_color = Color(0.8, 0.6, 0.4, 1)
roughness = 0.8

[sub_resource type="SphereMesh" id="SphereMesh_head"]
radius = 0.25
height = 0.5

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_head"]
albedo_color = Color(1, 0.8, 0.6, 1)
roughness = 0.7

[sub_resource type="CylinderMesh" id="CylinderMesh_limb"]
top_radius = 0.08
bottom_radius = 0.08
height = 0.4

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_limb"]
albedo_color = Color(1, 0.8, 0.6, 1)
roughness = 0.7

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_collision"]
radius = 0.3
height = 1.2

[node name="Character3D" type="CharacterBody3D"]
script = ExtResource("1_char3d")

[node name="Body" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.857553, 0)
mesh = SubResource("CapsuleMesh_body")
surface_material_override/0 = SubResource("StandardMaterial3D_body")

[node name="Head" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.62994, 0)
mesh = SubResource("SphereMesh_head")
surface_material_override/0 = SubResource("StandardMaterial3D_head")

[node name="LeftArm" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.4, 1.02119, 0)
mesh = SubResource("CylinderMesh_limb")
surface_material_override/0 = SubResource("StandardMaterial3D_limb")

[node name="RightArm" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.4, 1.02119, 0)
mesh = SubResource("CylinderMesh_limb")
surface_material_override/0 = SubResource("StandardMaterial3D_limb")

[node name="LeftLeg" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.15, 0.2, 0)
mesh = SubResource("CylinderMesh_limb")
surface_material_override/0 = SubResource("StandardMaterial3D_limb")

[node name="RightLeg" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.15, 0.2, 0)
mesh = SubResource("CylinderMesh_limb")
surface_material_override/0 = SubResource("StandardMaterial3D_limb")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.6, 0)
shape = SubResource("CapsuleShape3D_collision")

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 2, 3)
fov = 60.0
