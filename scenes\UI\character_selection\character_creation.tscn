[gd_scene load_steps=2 format=3 uid="uid://wr2xhojvwldv"]

[ext_resource type="Script" uid="uid://cdsnl3eq0yk3w" path="res://scenes/UI/character_selection/character_creation.gd" id="1_25oh0"]

[node name="character_creation" type="Control"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_25oh0")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -66.0
offset_top = -64.0
offset_right = 66.0
offset_bottom = 64.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="TilesLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "CHARACTER_CREATE_TITLE"
horizontal_alignment = 1

[node name="NameEdit" type="LineEdit" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "Enter Name"

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="MaleButton" type="Button" parent="VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "CHARACTER_GENDER_MALE"

[node name="FemaleButton" type="Button" parent="VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "CHARACTER_GENDER_FEMALE"

[node name="HBoxContainer2" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="BackButton" type="Button" parent="VBoxContainer/HBoxContainer2"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "UI_BACK"

[node name="CreateButton" type="Button" parent="VBoxContainer/HBoxContainer2"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "UI_CREATE"
