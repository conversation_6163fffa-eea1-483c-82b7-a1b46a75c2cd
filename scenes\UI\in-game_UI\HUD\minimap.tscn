[gd_scene load_steps=3 format=3 uid="uid://cbky412j2lqim"]

[ext_resource type="PackedScene" uid="uid://yc071iw5658w" path="res://scenes/UI/in-game_UI/HUD/map_view.tscn" id="1_m5u6n"]
[ext_resource type="Script" uid="uid://c1voiyprwl82c" path="res://scenes/UI/in-game_UI/HUD/minimap.gd" id="1_userf"]

[node name="Minimap" type="Control"]
clip_contents = true
layout_mode = 3
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -300.0
offset_bottom = 300.0
grow_horizontal = 0
script = ExtResource("1_userf")

[node name="Control" type="Control" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -300.0
offset_bottom = 300.0
grow_horizontal = 0

[node name="MapView" parent="Control" instance=ExtResource("1_m5u6n")]
unique_name_in_owner = true
layout_mode = 1
offset_right = 300.0
offset_bottom = 300.0
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_top = -24.0
offset_right = 36.0
grow_vertical = 0

[node name="ZoomInButton" type="Button" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "+"

[node name="ZoomOutButton" type="Button" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "-"
