[gd_scene load_steps=2 format=3 uid="uid://6dtfvs1lsvr2"]

[ext_resource type="Script" uid="uid://dlk2uye3ef221" path="res://scenes/UI/world_selection/world_creation.gd" id="1_036fx"]

[node name="world_creation" type="Control"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_036fx")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -70.5
offset_top = -46.5
offset_right = 70.5
offset_bottom = 46.5
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "WORLD_CREATE_TITLE"
horizontal_alignment = 1

[node name="NameEdit" type="LineEdit" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "WORLD_ENTER_NAME"

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="BackButton" type="Button" parent="VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "UI_BACK"

[node name="CreateButton" type="Button" parent="VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "UI_CREATE"
