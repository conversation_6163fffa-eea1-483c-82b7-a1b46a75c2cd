# scripts/characters/character_appearance.gd
extends Node

@export var head_sprite: Sprite2D
@export var torso_sprite: Sprite2D
# ... 다른 부위 스프라이트

var default_textures = {}

func _ready():
	# 초기화 시 기본 텍스처 저장
	if head_sprite:
		default_textures["helmet"] = head_sprite.texture # slot 이름과 일치시키기
	if torso_sprite:
		default_textures["armor"] = torso_sprite.texture # slot 이름과 일치시키기

# player.tscn의 시그널 연결과 일치하도록 함수 이름을 수정했습니다.
func _on_character_equipment_equipment_changed(equipment_data: EquipmentData, slot: String, is_equipped: bool):
	var target_sprite: Sprite2D = null

	match slot:
		"helmet":
			target_sprite = head_sprite # 헬멧은 머리 스프라이트를 변경
		"armor":
			target_sprite = torso_sprite
		# ... 다른 슬롯

	if not target_sprite:
		return

	if is_equipped:
		# 장착 시: EquipmentData에 정의된 텍스처로 변경
		if equipment_data.equipped_texture:
			target_sprite.texture = equipment_data.equipped_texture
	else:
		# 해제 시: 기본 텍스처로 복원
		target_sprite.texture = default_textures.get(slot, null)
