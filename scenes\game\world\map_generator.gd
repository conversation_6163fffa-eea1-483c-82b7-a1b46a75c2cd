# scenes/game/world/map_generator.gd
extends TileMapLayer

@export var min_x = -300
@export var max_x = 300
@export var min_y = -300
@export var max_y = 300

# 맵 생성에 필요한 내부 변수들
var tile_definitions: Dictionary = {}
var rng = RandomNumberGenerator.new()
var tile_noise = FastNoiseLite.new()
var river_noise = FastNoiseLite.new()
# [핵심 수정] lake_noise 변수 제거
# var lake_noise = FastNoiseLite.new()

func generate_world_data(world_seed_string: String) -> Dictionary:
	_setup_tile_definitions()
	_initialize_noises(world_seed_string)

	if BiomeManager:
		BiomeManager.initialize_with_seed(world_seed_string)

	var generated_tiles = {}
	var river_candidates = []

	# 1단계: 강을 제외한 기본 지형 생성
	for x in range(min_x, max_x + 1):
		for y in range(min_y, max_y + 1):
			var current_pos = Vector2i(x, y)
			var biome: BiomeData = BiomeManager.get_biome(current_pos)
			
			if biome:
				var chosen_tile_name = _get_tile_from_biome(biome, current_pos)
				
				if biome.name != "Ocean" and biome.name != "Lake":
					# [핵심 수정] 별도의 호수 생성 로직 완전히 제거
					
					# 강 생성 로직: 후보 위치만 기록
					var river_value = river_noise.get_noise_2d(current_pos.x, current_pos.y)
					var river_threshold = 0.04
					if abs(river_value) < river_threshold:
						river_candidates.append(current_pos)

				generated_tiles[current_pos] = chosen_tile_name

	# 2단계: 강 후보들을 후처리하여 부드러운 강줄기 생성
	for pos in river_candidates:
		var water_neighbor_count = 0
		var neighbors = [
			pos + Vector2i.RIGHT, pos + Vector2i.LEFT,
			pos + Vector2i.UP,   pos + Vector2i.DOWN
		]
		for n_pos in neighbors:
			if river_candidates.has(n_pos) or generated_tiles.get(n_pos) == "water":
				water_neighbor_count += 1
		
		if water_neighbor_count >= 2:
			generated_tiles[pos] = "water"

	# 3단계: 얼음 타일 적용 (최종 단계)
	for pos in generated_tiles:
		var tile_name = generated_tiles[pos]
		var biome: BiomeData = BiomeManager.get_biome(pos)
		
		# [수정] Ocean 바이옴 뿐만 아니라 Lake 바이옴도 얼음이 얼지 않도록 조건 추가
		if tile_name == "water" and biome.name != "Ocean" and biome.name != "Lake":
			var temperature = BiomeManager.get_temperature(pos)
			if temperature < 0:
				generated_tiles[pos] = "ice"
	
	return generated_tiles

# 타일의 종류와 TileSet 정보를 설정하는 함수
func _setup_tile_definitions():
	tile_definitions = {
		"dirt":   { "source_id": 0, "atlas_coords": Vector2i(0, 0) },
		"grass":  { "source_id": 1, "atlas_coords": Vector2i(0, 0) },
		"water":  { "source_id": 2, "atlas_coords": Vector2i(0, 0) },
		"ice":    { "source_id": 3, "atlas_coords": Vector2i(0, 0) },
		"snow":   { "source_id": 4, "atlas_coords": Vector2i(0, 0) }
	}

# 시드값을 기반으로 모든 노이즈 객체를 초기화하는 함수
func _initialize_noises(world_seed_string: String):
	var base_seed = world_seed_string.hash()
	rng.seed = base_seed

	tile_noise.seed = base_seed + 2
	tile_noise.noise_type = FastNoiseLite.TYPE_SIMPLEX
	tile_noise.frequency = 0.02
	tile_noise.fractal_octaves = 3

	river_noise.seed = base_seed + 3
	river_noise.noise_type = FastNoiseLite.TYPE_SIMPLEX
	river_noise.frequency = 0.0025
	river_noise.fractal_type = FastNoiseLite.FRACTAL_FBM
	river_noise.fractal_octaves = 3
	river_noise.domain_warp_enabled = true
	river_noise.domain_warp_type = FastNoiseLite.DOMAIN_WARP_SIMPLEX
	river_noise.domain_warp_amplitude = 25.0
	river_noise.domain_warp_frequency = 0.005

	# [핵심 수정] lake_noise 초기화 코드 제거
	# lake_noise.seed = base_seed + 4
	# lake_noise.noise_type = FastNoiseLite.TYPE_SIMPLEX
	# lake_noise.frequency = 0.008
	# lake_noise.fractal_octaves = 2

# ... (_get_tile_from_biome 함수는 변경 없음) ...
func _get_tile_from_biome(biome: BiomeData, pos: Vector2i) -> String:
	if not biome or biome.tile_weights.is_empty():
		return ""

	var noise_value = tile_noise.get_noise_2d(pos.x, pos.y)
	var normalized_value = (noise_value + 1.0) / 2.0

	var total_weight = 0.0
	for tile_weight_data in biome.tile_weights:
		total_weight += tile_weight_data.weight

	if total_weight <= 0:
		if not biome.tile_weights.is_empty():
			return biome.tile_weights[0].tile_name
		else:
			return ""

	var selection_point = normalized_value * total_weight
	for tile_weight_data in biome.tile_weights:
		if selection_point < tile_weight_data.weight:
			return tile_weight_data.tile_name
		selection_point -= tile_weight_data.weight

	if not biome.tile_weights.is_empty():
		return biome.tile_weights[-1].tile_name
	
	return ""
