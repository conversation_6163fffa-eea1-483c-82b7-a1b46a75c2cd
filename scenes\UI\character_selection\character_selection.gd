# scenes/UI/character_selection/character_selection.gd
extends Control

# 부모에게 보낼 신호들을 정의합니다.
signal character_selected
signal creation_requested
signal back_pressed

@export var character_slot_scene: PackedScene

@onready var character_list_container = %CharacterList
@onready var select_button = %SelectButton
@onready var create_button = %CreateButton
@onready var back_button = %BackButton
@onready var delete_confirmation_dialog: ConfirmationDialog = %DeleteConfirmationDialog

var character_to_delete: Dictionary
var selected_character_data: Dictionary

func _ready():
	select_button.disabled = true
	
	create_button.pressed.connect(_on_create_button_pressed)
	select_button.pressed.connect(_on_select_button_pressed)
	back_button.pressed.connect(_on_back_pressed)
	delete_confirmation_dialog.confirmed.connect(_on_delete_confirmation_dialog_confirmed)
	
	# 씬이 준비되면 캐릭터 목록을 채웁니다.
	populate_character_list()

# ▼▼▼▼▼ 여기가 수정된 부분입니다! ▼▼▼▼▼
func populate_character_list():
	for child in character_list_container.get_children():
		child.queue_free()
		
	# 1. SaveManager로부터 캐릭터 '이름'의 목록(Array[String])을 가져옵니다.
	var char_name_list = SaveManager.get_saved_character_list()
	
	# 2. 각 캐릭터 이름에 대해 반복합니다.
	for char_name in char_name_list:
		if not character_slot_scene:
			printerr("Character Slot Scene not set in the inspector!")
			return
			
		# 3. 이름으로 해당 캐릭터의 전체 데이터(Dictionary)를 불러옵니다.
		var char_data = SaveManager.load_character_data(char_name)
		if char_data.is_empty():
			continue # 데이터 로드에 실패하면 건너뜁니다.
			
		var slot = character_slot_scene.instantiate()
		character_list_container.add_child(slot)
		
		# 4. 이제 올바른 Dictionary 타입의 데이터를 슬롯에 전달합니다.
		slot.set_character_data(char_data)
		
		# 5. 신호에 전체 캐릭터 데이터를 바인딩합니다.
		slot.pressed.connect(_on_character_slot_selected.bind(char_data))
		slot.double_clicked.connect(_on_character_slot_double_clicked.bind(char_data))
		slot.delete_requested.connect(_on_delete_requested.bind(char_data))
# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

func _on_character_slot_selected(char_data: Dictionary):
	selected_character_data = char_data
	select_button.disabled = false

	for slot in character_list_container.get_children():
		if slot.character_data == selected_character_data:
			slot.modulate = Color.WHITE
		else:
			slot.modulate = Color(0.6, 0.6, 0.6)

func _on_character_slot_double_clicked(char_data: Dictionary):
	selected_character_data = char_data
	_on_select_button_pressed()

func _on_create_button_pressed():
	emit_signal("creation_requested")

func _on_back_pressed():
	emit_signal("back_pressed")

func _on_select_button_pressed():
	if selected_character_data.is_empty():
		return
	
	GameManager.current_character_name = selected_character_data.get("name")
	GameManager.selected_character_gender = selected_character_data.get("gender")
	emit_signal("character_selected")

func _on_delete_requested(char_data: Dictionary):
	character_to_delete = char_data
	var char_name = character_to_delete.get("name", tr("UI_NOT_APPLICABLE"))
	delete_confirmation_dialog.dialog_text = tr("CHARACTER_DELETE_CONFIRM_TEXT") % char_name
	delete_confirmation_dialog.popup_centered()

func _on_delete_confirmation_dialog_confirmed():
	if character_to_delete.is_empty():
		return
		
	var char_name = character_to_delete.get("name")
	if char_name:
		SaveManager.delete_character(char_name)
		
	populate_character_list()
	
	selected_character_data = {}
	character_to_delete = {}
	select_button.disabled = true
