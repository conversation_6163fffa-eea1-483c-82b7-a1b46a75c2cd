# MapInteractionHandler.gd
extends ColorRect

@onready var map_view: Control = %MapView

@export var min_zoom := 0.2
@export var max_zoom := 10.0
@export var zoom_speed := 0.23

var is_dragging := false

func _ready():
	mouse_filter = Control.MOUSE_FILTER_STOP

func _gui_input(event: InputEvent):
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP and event.is_pressed():
			zoom_in()
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN and event.is_pressed():
			zoom_out()
		elif event.button_index == MOUSE_BUTTON_LEFT:
			is_dragging = event.is_pressed()
	
	if event is InputEventMouseMotion and is_dragging:
		map_view.position += event.relative
		clamp_map_position()

func zoom_in():
	apply_zoom(1 + zoom_speed)

func zoom_out():
	apply_zoom(1 - zoom_speed)

func apply_zoom(zoom_factor: float):
	if not is_instance_valid(map_view): return
	
	var new_scale = map_view.scale * zoom_factor
	new_scale = new_scale.clamp(Vector2.ONE * min_zoom, Vector2.ONE * max_zoom)

	var mouse_pos = get_local_mouse_position()
	var point_before_zoom = (mouse_pos - map_view.position) / map_view.scale
	var point_after_zoom = point_before_zoom * new_scale
	map_view.position = mouse_pos - point_after_zoom
	
	map_view.scale = new_scale
	clamp_map_position()

func clamp_map_position():
	if not is_instance_valid(map_view): return

	var map_size_scaled = map_view.size * map_view.scale
	var view_rect = get_rect()

	if map_size_scaled.x > view_rect.size.x:
		map_view.position.x = clampf(map_view.position.x, view_rect.size.x - map_size_scaled.x, 0)
	else:
		map_view.position.x = clampf(map_view.position.x, 0, view_rect.size.x - map_size_scaled.x)

	if map_size_scaled.y > view_rect.size.y:
		map_view.position.y = clampf(map_view.position.y, view_rect.size.y - map_size_scaled.y, 0)
	else:
		map_view.position.y = clampf(map_view.position.y, 0, view_rect.size.y - map_size_scaled.y)
