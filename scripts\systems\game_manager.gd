# scripts/systems/game_manager.gd
extends Node

var current_character_name = ""
var current_world_name = ""
var player_node = null
var selected_character_gender = "male"

func _ready():
	get_window().close_requested.connect(save_and_exit_to_main_menu)

func new_game(world_name: String):
	if current_character_name.is_empty():
		printerr("새 게임 시작 오류: 캐릭터가 선택되지 않았습니다!")
		current_character_name = "Player" # 비상용 기본값
	
	current_world_name = world_name
	
	var world_data = SaveManager.load_world_data(world_name)
	if world_data.is_empty():
		world_data = {"world_name": world_name}
		SaveManager.save_world_data(world_data, world_name)
	
	var char_data = SaveManager.load_character_data(current_character_name)
	if not char_data.has("name"):
		char_data["name"] = current_character_name
	if not char_data.has("exploration_data"):
		char_data["exploration_data"] = {}
	char_data["exploration_data"][current_world_name] = []
	SaveManager.save_character_data(char_data, current_character_name)
	
	ExplorationManager.clear_data()
	get_tree().change_scene_to_file("res://scenes/game/game.tscn")

func load_game(world_name: String):
	if current_character_name.is_empty():
		printerr("게임 불러오기 오류: 캐릭터가 선택되지 않았습니다!")
		current_character_name = "Player" # 비상용 기본값

	current_world_name = world_name
	
	var character_data = SaveManager.load_character_data(current_character_name)
	if character_data.is_empty():
		new_game(world_name)
		return

	var exploration_data_by_world = character_data.get("exploration_data", {})
	var this_world_map_data_array = exploration_data_by_world.get(current_world_name, [])
	
	var revealed_tiles_dict = {}
	for tile_coords_array in this_world_map_data_array:
		if tile_coords_array is Array and tile_coords_array.size() == 2:
			revealed_tiles_dict[Vector2i(tile_coords_array[0], tile_coords_array[1])] = true
	
	ExplorationManager.load_revealed_tiles(revealed_tiles_dict)
	get_tree().change_scene_to_file("res://scenes/game/game.tscn")

# ▼▼▼▼▼ 여기가 최종 수정된 부분입니다! ▼▼▼▼▼
func save_and_exit_to_main_menu():
	if get_tree().current_scene.scene_file_path != "res://scenes/game/game.tscn":
		get_tree().quit()
		return

	print("Saving character data before exiting...")

	# 1. 현재 캐릭터의 저장된 데이터를 불러옵니다.
	var character_data = SaveManager.load_character_data(current_character_name)
	
	# 2. ExplorationManager로부터 '현재 플레이 중인' 탐험 데이터를 가져옵니다.
	var exploration_data_to_save = ExplorationManager.get_data_for_saving()
	
	# 3. 캐릭터 데이터 안에 탐험 데이터 구조가 없으면 새로 만듭니다.
	if not character_data.has("exploration_data"):
		character_data["exploration_data"] = {}
	
	# 4. 현재 월드의 탐험 기록을 최신 데이터로 덮어씁니다.
	character_data["exploration_data"][current_world_name] = exploration_data_to_save
	
	# 5. 모든 정보가 합쳐진 최종 캐릭터 데이터를 파일에 저장합니다.
	SaveManager.save_character_data(character_data, current_character_name)
	
	get_tree().change_scene_to_file("res://scenes/UI/main_menu/main_menu.tscn")
