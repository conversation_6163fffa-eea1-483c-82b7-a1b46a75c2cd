# scripts/systems/mana_system.gd
extends Node
class_name ManaSystem

# --- 신호 정의 ---
signal mana_changed(current_mana)
signal max_mana_changed(new_max_mana) # [추가] 최대 마나가 바뀔 때를 위한 신호
signal mana_depleted # 마나가 모두 소진되었을 때 보낼 신호

# --- 변수 ---
var current_mana: float
var max_mana: float:
	# max_mana 변수의 값이 변경될 때마다 자동으로 이 코드를 실행합니다. (Setter)
	set(value):
		if max_mana != value:
			max_mana = value
			max_mana_changed.emit(max_mana) # 값이 바뀌면 신호를 보냅니다.

# --- 노드 참조 ---
@onready var stat_system: StatSystem = get_parent().get_node("stat_system")


func _ready():
	if stat_system:
		# [중요] StatSystem이 보내는 신호에 연결하여 max_mana를 동적으로 업데이트합니다.
		# stat_system 스크립트의 실제 신호 이름에 따라 아래 이름을 수정해야 할 수 있습니다.
		stat_system.current_max_mana_changed.connect(_on_max_mana_changed)
		
		# 초기값 설정 (Setter를 통해 신호가 발생하도록 self를 사용)
		self.max_mana = stat_system.current_max_mana
		current_mana = max_mana
	else:
		push_error("ManaSystem requires a StatSystem to function!")


# StatSystem으로부터 최대 마나가 변경되었다는 신호를 받으면 이 함수가 실행됩니다.
func _on_max_mana_changed(new_max_mana: float):
	# Setter를 호출하여 max_mana 값을 업데이트하고 신호를 발생시킵니다.
	self.max_mana = new_max_mana
	
	# 현재 마나가 새 최대 마나보다 높으면 조정해줍니다.
	if current_mana > max_mana:
		current_mana = max_mana
		mana_changed.emit(current_mana)


# 마나를 소모하는 함수
func spend_mana(amount: float) -> bool:
	if current_mana >= amount:
		current_mana -= amount
		mana_changed.emit(current_mana)
		
		if current_mana == 0:
			mana_depleted.emit()
			
		return true # 마나 소모 성공
	else:
		return false # 마나 부족으로 소모 실패

# 마나를 회복하는 함수
func regenerate_mana(amount: float):
	current_mana = min(current_mana + amount, max_mana)
	mana_changed.emit(current_mana)
