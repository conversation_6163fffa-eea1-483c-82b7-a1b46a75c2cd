# scripts/systems/exploration_manager.gd
extends Node

signal map_exploration_updated(newly_revealed_tiles)

var revealed_tiles: Dictionary = {}

func reveal_tiles(tiles_in_sight: Array):
	var newly_revealed_tiles = []
	for tile_pos in tiles_in_sight:
		if not revealed_tiles.has(tile_pos):
			revealed_tiles[tile_pos] = true
			newly_revealed_tiles.append(tile_pos)
	
	if not newly_revealed_tiles.is_empty():
		map_exploration_updated.emit(newly_revealed_tiles)

# [수정] 이제 이 함수는 신호를 보내지 않고, 데이터를 저장만 합니다.
func load_revealed_tiles(loaded_data: Dictionary):
	revealed_tiles = loaded_data
	# emit_signal 코드를 삭제했습니다.

# [추가] 저장된 탐험 데이터를 다른 스크립트에 제공하는 함수
func get_initial_revealed_tiles() -> Array:
	return revealed_tiles.keys()

func get_data_for_saving() -> Array:
	var serializable_data = []
	for tile_pos in revealed_tiles.keys():
		serializable_data.append([tile_pos.x, tile_pos.y])
	return serializable_data

func clear_data():
	revealed_tiles.clear()
