[gd_scene load_steps=2 format=3 uid="uid://crl8efh8gn4rn"]

[ext_resource type="Script" uid="uid://c6d6r8d3cw2ns" path="res://scenes/UI/in-game_UI/character_stats_panel.gd" id="1_ubh34"]

[node name="CharacterStatsPanel" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_ubh34")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="PanelContainer" type="PanelContainer" parent="CanvasLayer"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -28.5
offset_top = -106.0
offset_right = 28.5
offset_bottom = 106.0
grow_horizontal = 2
grow_vertical = 2

[node name="MarginContainer" type="MarginContainer" parent="CanvasLayer/PanelContainer"]
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="CanvasLayer/PanelContainer/MarginContainer"]
layout_mode = 2

[node name="Health" type="HBoxContainer" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="HealthNameLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/Health"]
unique_name_in_owner = true
layout_mode = 2

[node name="HealthValueLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/Health"]
unique_name_in_owner = true
layout_mode = 2

[node name="Mana" type="HBoxContainer" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="ManaNameLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/Mana"]
unique_name_in_owner = true
layout_mode = 2

[node name="ManaValueLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/Mana"]
unique_name_in_owner = true
layout_mode = 2

[node name="AttackDamage" type="HBoxContainer" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="AttackDamageNameLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/AttackDamage"]
unique_name_in_owner = true
layout_mode = 2

[node name="AttackDamageValueLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/AttackDamage"]
unique_name_in_owner = true
layout_mode = 2

[node name="Armor" type="HBoxContainer" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="ArmorNameLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/Armor"]
unique_name_in_owner = true
layout_mode = 2

[node name="ArmorValueLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/Armor"]
unique_name_in_owner = true
layout_mode = 2
text = "Defense"

[node name="MovementSpeed" type="HBoxContainer" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="MovementSpeedNameLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/MovementSpeed"]
unique_name_in_owner = true
layout_mode = 2

[node name="MovementSpeedValueLabel" type="Label" parent="CanvasLayer/PanelContainer/MarginContainer/VBoxContainer/MovementSpeed"]
unique_name_in_owner = true
layout_mode = 2
