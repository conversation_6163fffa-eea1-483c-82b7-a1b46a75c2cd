[gd_resource type="AnimationLibrary" load_steps=14 format=3 uid="uid://cow3yjuvcjtem"]

[ext_resource type="Texture2D" uid="uid://cir4y7w0a7nv5" path="res://assets/graphic/character/naked/female/female_front_torso.png" id="1_gvacd"]
[ext_resource type="Texture2D" uid="uid://b8dci17trw0ev" path="res://assets/graphic/character/naked/female/female_front_head.png" id="2_io0cj"]
[ext_resource type="Texture2D" uid="uid://dcvfmfid8rhpt" path="res://assets/graphic/character/naked/female/female_front_armR.png" id="3_6twee"]
[ext_resource type="Texture2D" uid="uid://ky84efmd6dh7" path="res://assets/graphic/character/naked/female/female_front_armL.png" id="4_yyv03"]
[ext_resource type="Texture2D" uid="uid://bchldw6bfothl" path="res://assets/graphic/character/naked/female/female_front_legR.png" id="5_we1ho"]
[ext_resource type="Texture2D" uid="uid://b6k3oopmys7f7" path="res://assets/graphic/character/naked/female/female_front_legL.png" id="6_loomw"]

[sub_resource type="Animation" id="Animation_dubqk"]
resource_name = "RESET"
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Visuals/head:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("2_io0cj")]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Visuals/torso:texture")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("1_gvacd")]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Visuals/armR:texture")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("3_6twee")]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Visuals/armL:texture")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("4_yyv03")]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Visuals/legR:texture")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("5_we1ho")]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Visuals/legL:texture")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("6_loomw")]
}

[sub_resource type="Animation" id="Animation_ekxr8"]
resource_name = "idle_down"
loop_mode = 1

[sub_resource type="Animation" id="Animation_pfg7y"]
resource_name = "idle_side"
loop_mode = 1

[sub_resource type="Animation" id="Animation_3lpf4"]
resource_name = "idle_up"
loop_mode = 1

[sub_resource type="Animation" id="Animation_ub38x"]
resource_name = "walk_down"
loop_mode = 1

[sub_resource type="Animation" id="Animation_q7bvs"]
resource_name = "walk_side"
length = 1.33334
loop_mode = 1

[sub_resource type="Animation" id="Animation_l8tpy"]
resource_name = "walk_up"
loop_mode = 1

[resource]
_data = {
&"RESET": SubResource("Animation_dubqk"),
&"idle_down": SubResource("Animation_ekxr8"),
&"idle_side": SubResource("Animation_pfg7y"),
&"idle_up": SubResource("Animation_3lpf4"),
&"walk_down": SubResource("Animation_ub38x"),
&"walk_side": SubResource("Animation_q7bvs"),
&"walk_up": SubResource("Animation_l8tpy")
}
