[gd_scene load_steps=3 format=3 uid="uid://ni5l4h6w2dx"]

[ext_resource type="PackedScene" uid="uid://hej54n1wkswk" path="res://scenes/UI/in-game_UI/HUD/StatBar.tscn" id="1_ym4hr"]
[ext_resource type="PackedScene" uid="uid://cbky412j2lqim" path="res://scenes/UI/in-game_UI/HUD/minimap.tscn" id="2_7m85e"]

[node name="PlayerHUD" type="CanvasLayer"]
layer = 3

[node name="HealthBar" parent="." instance=ExtResource("1_ym4hr")]
bar_color = Color(1, 0, 0, 1)

[node name="ManaBar" parent="." instance=ExtResource("1_ym4hr")]
offset_top = 89.0
offset_bottom = 89.0
bar_color = Color(0, 0, 1, 1)

[node name="Minimap" parent="." instance=ExtResource("2_7m85e")]
