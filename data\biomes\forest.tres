[gd_resource type="Resource" script_class="BiomeData" load_steps=5 format=3 uid="uid://5xm3ih7g6k4e"]

[ext_resource type="Script" uid="uid://cv0pplcsu11ts" path="res://scripts/data_definitions/biome_data.gd" id="1_s8i2t"]
[ext_resource type="Script" uid="uid://ytux6w48rnbr" path="res://scripts/data_definitions/tilew_eight_data.gd" id="2_hyvvr"]

[sub_resource type="Resource" id="Resource_hwrsq"]
script = ExtResource("2_hyvvr")
tile_name = "dirt"
weight = 1.0
metadata/_custom_type_script = "uid://ytux6w48rnbr"

[sub_resource type="Resource" id="Resource_hyvvr"]
script = ExtResource("2_hyvvr")
tile_name = "grass"
weight = 3.0
metadata/_custom_type_script = "uid://ytux6w48rnbr"

[resource]
script = ExtResource("1_s8i2t")
biome_name = "Forsest"
name = "Forsest"
tile_weights = Array[ExtResource("2_hyvvr")]([SubResource("Resource_hwrsq"), SubResource("Resource_hyvvr")])
optimal_temperature = 0.5
optimal_humidity = 0.1
metadata/_custom_type_script = "uid://cv0pplcsu11ts"
