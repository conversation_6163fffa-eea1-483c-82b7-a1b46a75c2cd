[gd_resource type="Resource" script_class="CharacterStats" load_steps=2 format=3 uid="uid://w4p3aaqo4fb5"]

[ext_resource type="Script" uid="uid://covoxmskvt8i" path="res://scripts/data_definitions/character_stats.gd" id="1_a3dxu"]

[resource]
script = ExtResource("1_a3dxu")
max_health = 100.0
max_mana = 50.0
attack_power = 10.0
attack_speed = 1.0
cast_speed = 1.0
crit_chance = 5.0
crit_damage = 150.0
defense = 5.0
health_regen_per_tick = 0.5
mana_regen_per_tick = 0.5
move_speed = 800.0
metadata/_custom_type_script = "uid://covoxmskvt8i"
