# scripts/data_definitions/character_stats.gd
extends Resource
class_name CharacterStats

# --- 기본 능력치 ---
@export_group("Primary Stats")
@export var max_health: float = 100.0
@export var max_mana: float = 50.0

# --- 공격 관련 능력치 ---
@export_group("Offensive Stats")
@export var attack_power: float = 10.0
@export var attack_speed: float = 1.0   # 초당 공격 횟수
@export var cast_speed: float = 1.0     # 초당 시전 횟수
@export var crit_chance: float = 5.0    # 백분율 (%)
@export var crit_damage: float = 150.0  # 백분율 (%, 기본 150%)

# --- 방어 및 생존 관련 능력치 ---
@export_group("Defensive Stats")
@export var defense: float = 5.0
@export var health_regen_per_tick: float = 0.5
@export var mana_regen_per_tick: float = 0.5

# --- 유틸리티 능력치 ---
@export_group("Utility Stats")
@export var move_speed: float = 120.0
