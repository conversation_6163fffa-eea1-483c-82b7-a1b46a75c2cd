# 테라리아 스타일 세계 생성 화면

## 개요
이 기능은 테라리아와 같은 세계 생성 진행 상황을 시각적으로 보여주는 화면을 추가합니다. 사용자가 새로운 세계를 생성할 때, 생성 과정을 단계별로 확인할 수 있습니다.

## 주요 기능

### 1. 세계 생성 화면 (WorldGenerationScreen)
- **위치**: `scenes/UI/world_generation/world_generation_screen.tscn`
- **스크립트**: `scenes/UI/world_generation/world_generation_screen.gd`

#### 특징:
- 6단계의 세계 생성 과정을 시각적으로 표시
- 부드러운 진행률 바 애니메이션
- 다국어 지원 (한국어/영어)
- ESC 키로 취소 불가 (생성 중에는 중단할 수 없음)

#### 생성 단계:
1. **초기화** (0.5초) - `WORLD_GENERATION_INITIALIZING`
2. **바이옴 생성** (1.5초) - `WORLD_GENERATION_BIOMES`
3. **지형 생성** (2.0초) - `WORLD_GENERATION_TERRAIN`
4. **강 생성** (1.0초) - `WORLD_GENERATION_RIVERS`
5. **호수 생성** (0.8초) - `WORLD_GENERATION_LAKES`
6. **마무리** (1.2초) - `WORLD_GENERATION_FINALIZING`

### 2. 번역 시스템 통합
새로운 번역 키들이 추가되었습니다:

#### 영어 (en.csv):
```csv
WORLD_GENERATION_TITLE,"Generating World..."
WORLD_GENERATION_INITIALIZING,"Initializing..."
WORLD_GENERATION_BIOMES,"Generating biomes..."
WORLD_GENERATION_TERRAIN,"Creating terrain..."
WORLD_GENERATION_RIVERS,"Carving rivers..."
WORLD_GENERATION_LAKES,"Filling lakes..."
WORLD_GENERATION_FINALIZING,"Finalizing world..."
WORLD_GENERATION_COMPLETE,"World generation complete!"
WORLD_GENERATION_INFO,"Please wait while the world is being generated.\nThis may take a few moments..."
```

#### 한국어 (ko.csv):
```csv
WORLD_GENERATION_TITLE,"세계 생성 중..."
WORLD_GENERATION_INITIALIZING,"초기화 중..."
WORLD_GENERATION_BIOMES,"바이옴 생성 중..."
WORLD_GENERATION_TERRAIN,"지형 생성 중..."
WORLD_GENERATION_RIVERS,"강 생성 중..."
WORLD_GENERATION_LAKES,"호수 생성 중..."
WORLD_GENERATION_FINALIZING,"세계 마무리 중..."
WORLD_GENERATION_COMPLETE,"세계 생성 완료!"
WORLD_GENERATION_INFO,"세계를 생성하는 동안 잠시 기다려 주세요.\n몇 분 정도 소요될 수 있습니다..."
```

### 3. GameManager 수정
새로운 함수가 추가되었습니다:
- `new_game_with_generation_screen(world_name: String)`: 세계 생성 화면을 통해 새 게임을 시작

### 4. 통합 방식
- 기존 `world_creation.gd`에서 `GameManager.new_game()` 대신 `GameManager.new_game_with_generation_screen()` 호출
- 메인메뉴의 기존 흐름을 유지하면서 새로운 기능 추가
- 기존 기능들에 영향을 주지 않음

## 사용 방법

1. 메인메뉴에서 "게임 시작" 선택
2. 캐릭터 선택 또는 생성
3. 세계 선택 화면에서 "새로 만들기" 선택
4. 세계 이름 입력 후 "생성" 버튼 클릭
5. 세계 생성 화면이 표시되며 진행 상황을 확인
6. 생성 완료 후 자동으로 게임 화면으로 전환

## 기술적 세부사항

### 애니메이션
- `Tween`을 사용한 부드러운 진행률 바 애니메이션
- 각 단계별 적절한 지속 시간 설정

### 안정성
- 생성 중 ESC 키 입력 차단
- 기존 세계 생성 로직과 완전 호환
- 오류 처리 및 예외 상황 대응

### 확장성
- 새로운 생성 단계 추가 용이
- 번역 키 기반으로 다른 언어 추가 가능
- 애니메이션 및 시각 효과 커스터마이징 가능

## 파일 구조
```
scenes/UI/world_generation/
├── world_generation_screen.tscn    # 세계 생성 화면 씬
└── world_generation_screen.gd      # 세계 생성 화면 로직

scripts/systems/
└── game_manager.gd                 # 수정된 게임 매니저

scenes/UI/world_selection/
└── world_creation.gd               # 수정된 세계 생성 패널

translations/
├── en.csv                          # 영어 번역 (수정됨)
└── ko.csv                          # 한국어 번역 (수정됨)
```
