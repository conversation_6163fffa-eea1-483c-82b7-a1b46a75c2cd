# scenes/UI/character_selection/character_slot.gd
extends Button

signal double_clicked
signal delete_requested

@onready var name_label: Label = %Name<PERSON>abel
@onready var gender_label: Label = %GenderLabel
@onready var delete_button: Button = %DeleteButton

var character_data: Dictionary

func _ready():
	delete_button.pressed.connect(_on_delete_pressed)

func set_character_data(data: Dictionary):
	character_data = data
	# [변경] 이름이 없을 경우를 대비한 기본값을 번역 키로 설정합니다.
	name_label.text = character_data.get("name", tr("UI_NOT_APPLICABLE"))
	
	# [변경] 성별 데이터("male", "female")에 따라 올바른 번역 키를 사용합니다.
	var gender_key = character_data.get("gender", "n/a")
	match gender_key:
		"male":
			gender_label.text = tr("CHARACTER_GENDER_MALE")
		"female":
			gender_label.text = tr("CHARACTER_GENDER_FEMALE")
		_:
			gender_label.text = tr("UI_NOT_APPLICABLE")


func _gui_input(event: InputEvent):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT and event.double_click:
		if not delete_button.is_hovered():
			emit_signal("double_clicked")
			get_viewport().set_input_as_handled()

func _on_delete_pressed():
	emit_signal("delete_requested")
