# scenes/3d_test/character_3d.gd
extends CharacterBody3D

# 4등신 캐릭터 컨트롤러
const SPEED = 5.0
const JUMP_VELOCITY = 8.0
const ROTATION_SPEED = 3.0

# 중력 설정
var gravity = ProjectSettings.get_setting("physics/3d/default_gravity")

# 캐릭터 부위 노드들
@onready var body_mesh = $Body
@onready var head_mesh = $Head
@onready var left_arm = $LeftArm
@onready var right_arm = $RightArm
@onready var left_leg = $LeftLeg
@onready var right_leg = $RightLeg
@onready var camera = $Camera3D

# 애니메이션 변수들
var head_bob_time = 0.0
var is_moving = false

func _ready():
	# 4등신 비율로 캐릭터 설정
	_setup_character_proportions()

func _setup_character_proportions():
	# 4등신 비율: 머리 1, 몸통 3
	# 전체 높이를 1.5로 설정 (머리 0.375, 몸통 1.125)
	
	# 머리 크기와 위치 조정
	head_mesh.scale = Vector3(1.2, 1.0, 1.2)  # 머리를 약간 크게
	head_mesh.position.y = 1.1  # 머리 위치
	
	# 몸통 크기와 위치 조정
	body_mesh.scale = Vector3(1.0, 0.8, 1.0)  # 몸통을 약간 납작하게
	body_mesh.position.y = 0.4  # 몸통 위치



func _physics_process(delta):
	# 중력 적용
	if not is_on_floor():
		velocity.y -= gravity * delta

	# 점프 처리
	if Input.is_action_just_pressed("ui_accept") and is_on_floor():
		velocity.y = JUMP_VELOCITY

	# 이동 입력 처리
	var input_dir = Input.get_vector("ui_left", "ui_right", "ui_up", "ui_down")
	var direction = (transform.basis * Vector3(input_dir.x, 0, input_dir.y)).normalized()
	
	if direction:
		velocity.x = direction.x * SPEED
		velocity.z = direction.z * SPEED
		is_moving = true
		
		# 캐릭터 회전 (이동 방향으로)
		var target_rotation = atan2(-direction.x, -direction.z)
		rotation.y = lerp_angle(rotation.y, target_rotation, ROTATION_SPEED * delta)
	else:
		velocity.x = move_toward(velocity.x, 0, SPEED)
		velocity.z = move_toward(velocity.z, 0, SPEED)
		is_moving = false

	move_and_slide()
	
	# 애니메이션 업데이트
	_update_animations(delta)

func _update_animations(delta):
	# 걷기 애니메이션 (걸을 때)
	if is_moving and is_on_floor():
		head_bob_time += delta * 8.0
		var bob_offset = sin(head_bob_time) * 0.05
		var arm_swing = sin(head_bob_time) * 0.3
		var leg_swing = sin(head_bob_time + PI) * 0.2

		# 머리와 몸통 흔들기
		head_mesh.position.y = 1.1 + bob_offset
		body_mesh.position.y = 0.4 + bob_offset * 0.5

		# 팔 흔들기 (반대로)
		left_arm.rotation.x = arm_swing
		right_arm.rotation.x = -arm_swing

		# 다리 흔들기 (반대로)
		left_leg.rotation.x = leg_swing
		right_leg.rotation.x = -leg_swing
	else:
		# 정지 시 원래 위치로 복귀
		head_mesh.position.y = lerp(head_mesh.position.y, 1.1, delta * 5.0)
		body_mesh.position.y = lerp(body_mesh.position.y, 0.4, delta * 5.0)

		# 팔다리 원래 위치로
		left_arm.rotation.x = lerp(left_arm.rotation.x, 0.0, delta * 5.0)
		right_arm.rotation.x = lerp(right_arm.rotation.x, 0.0, delta * 5.0)
		left_leg.rotation.x = lerp(left_leg.rotation.x, 0.0, delta * 5.0)
		right_leg.rotation.x = lerp(right_leg.rotation.x, 0.0, delta * 5.0)

		head_bob_time = 0.0

func _input(event):
	# 마우스로 카메라 회전
	if event is InputEventMouseMotion and Input.is_action_pressed("ui_select"):
		rotate_y(-event.relative.x * 0.01)
		camera.rotate_x(-event.relative.y * 0.01)
		camera.rotation.x = clamp(camera.rotation.x, -PI/3, PI/6)
