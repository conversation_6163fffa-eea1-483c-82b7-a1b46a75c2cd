[gd_scene load_steps=7 format=3 uid="uid://ck9p4r7s2vx8"]

[ext_resource type="Script" uid="uid://dx7m9k4p2w8v" path="res://scenes/3d_test/test_world_3d.gd" id="1_test3d"]
[ext_resource type="PackedScene" uid="uid://bk8m3n5qr9vx" path="res://scenes/3d_test/character_3d.tscn" id="1_char"]

[sub_resource type="Environment" id="Environment_1"]
background_mode = 1
background_color = Color(0.5, 0.7, 1, 1)
ambient_light_source = 2
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_energy = 0.3

[sub_resource type="PlaneMesh" id="PlaneMesh_ground"]
size = Vector2(20, 20)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ground"]
albedo_color = Color(0.2, 0.8, 0.2, 1)

[sub_resource type="BoxMesh" id="BoxMesh_platform"]
size = Vector3(2, 0.2, 2)

[sub_resource type="BoxShape3D" id="BoxShape3D_ground"]
size = Vector3(20, 0.1, 20)

[sub_resource type="BoxShape3D" id="BoxShape3D_platform"]
size = Vector3(2, 0.2, 2)

[node name="TestWorld3D" type="Node3D"]
script = ExtResource("1_test3d")

[node name="Environment" type="Node3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_1")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 5, 0)
light_energy = 1.0
shadow_enabled = true

[node name="Ground" type="StaticBody3D" parent="."]

[node name="GroundMesh" type="MeshInstance3D" parent="Ground"]
mesh = SubResource("PlaneMesh_ground")
surface_material_override/0 = SubResource("StandardMaterial3D_ground")

[node name="GroundCollision" type="CollisionShape3D" parent="Ground"]
shape = SubResource("BoxShape3D_ground")

[node name="Platforms" type="Node3D" parent="."]

[node name="Platform1" type="StaticBody3D" parent="Platforms"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0.5, 0)

[node name="PlatformMesh" type="MeshInstance3D" parent="Platforms/Platform1"]
mesh = SubResource("BoxMesh_platform")

[node name="PlatformCollision" type="CollisionShape3D" parent="Platforms/Platform1"]
shape = SubResource("BoxShape3D_platform")

[node name="Platform2" type="StaticBody3D" parent="Platforms"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 1.0, 3)

[node name="PlatformMesh" type="MeshInstance3D" parent="Platforms/Platform2"]
mesh = SubResource("BoxMesh_platform")

[node name="PlatformCollision" type="CollisionShape3D" parent="Platforms/Platform2"]
shape = SubResource("BoxShape3D_platform")

[node name="Platform3" type="StaticBody3D" parent="Platforms"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.5, -4)

[node name="PlatformMesh" type="MeshInstance3D" parent="Platforms/Platform3"]
mesh = SubResource("BoxMesh_platform")

[node name="PlatformCollision" type="CollisionShape3D" parent="Platforms/Platform3"]
shape = SubResource("BoxShape3D_platform")

[node name="Character3D" parent="." instance=ExtResource("1_char")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)

[node name="UI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="Instructions" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -120.0
offset_right = 400.0
offset_bottom = -10.0
text = "3D 4등신 캐릭터 테스트

조작법:
- WASD 또는 방향키: 이동
- 스페이스바: 점프
- 마우스 드래그 (클릭한 상태): 카메라 회전
- ESC: 메인메뉴로 돌아가기"
vertical_alignment = 2

[node name="BackButton" type="Button" parent="UI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -100.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = 40.0
text = "메인메뉴"
